{% extends 'base.html' %}
{% load static %}
{% load warehouse_extras %}

{% block title %}
    {% if user_language == 'en' %}Advanced Warehouse Dashboard{% else %}لوحة تحكم المخازن المتقدمة{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        min-height: 100vh;
    }

    .warehouse-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 10px 30px rgba(31, 38, 135, 0.37);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
        padding: 1rem;
    }

    .stats-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2.5rem;
        border: 2px solid rgba(255, 182, 193, 0.3);
        box-shadow:
            0 25px 50px rgba(255, 182, 193, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            inset 0 -1px 0 rgba(255, 182, 193, 0.1);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 182, 193, 0.3),
            transparent);
        transition: left 0.6s ease;
    }

    .stats-card::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle,
            rgba(255,255,255,0.1) 0%,
            transparent 70%);
        opacity: 0;
        transition: opacity 0.4s ease;
        pointer-events: none;
    }

    .stats-card:hover::before {
        left: 100%;
    }

    .stats-card:hover::after {
        opacity: 1;
    }

    .stats-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow:
            0 35px 70px rgba(255, 182, 193, 0.4),
            0 0 50px rgba(255, 105, 180, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 105, 180, 0.4);
    }

    .stat-number {
        font-size: 4rem;
        font-weight: 900;
        background: linear-gradient(135deg,
            #ff6b6b 0%,
            #ff8e8e 50%,
            #ffa8a8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.8rem;
        line-height: 1;
        text-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
        animation: numberGlow 3s ease-in-out infinite alternate;
    }

    @keyframes numberGlow {
        0% { filter: brightness(1) drop-shadow(0 0 10px rgba(255, 107, 107, 0.3)); }
        100% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 107, 107, 0.6)); }
    }

    .stat-label {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }

    .stat-change {
        font-size: 1rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .stat-change.positive {
        background: rgba(46, 204, 113, 0.2);
        color: #27ae60;
        border: 1px solid rgba(46, 204, 113, 0.4);
        box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    }

    .stat-change.negative {
        background: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
        border: 1px solid rgba(231, 76, 60, 0.4);
        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    }

    .stat-icon {
        position: absolute;
        top: 2rem;
        right: 2rem;
        font-size: 4rem;
        opacity: 0.3;
        background: linear-gradient(135deg,
            #ff6b6b 0%,
            #ff8e8e 50%,
            #ffa8a8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.4));
        animation: iconFloat 4s ease-in-out infinite;
    }

    @keyframes iconFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(5deg); }
    }

    /* Advanced Statistics Styles */
    .advanced-stats-section {
        margin: 3rem 0;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        border-radius: 30px;
        border: 1px solid rgba(255, 182, 193, 0.3);
        box-shadow: 0 20px 40px rgba(255, 182, 193, 0.2);
    }

    .stats-mega-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .mega-stat-card {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(255, 240, 245, 0.8) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(255, 182, 193, 0.3);
        position: relative;
        overflow: hidden;
        transition: all 0.4s ease;
    }

    .mega-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg,
            transparent 30%,
            rgba(255, 182, 193, 0.2) 50%,
            transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .mega-stat-card:hover::before {
        transform: translateX(100%);
    }

    .mega-stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(255, 182, 193, 0.3);
        border-color: rgba(255, 105, 180, 0.4);
    }

    .mega-stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
    }

    .mega-stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
    }

    .mega-stat-trend {
        font-size: 0.9rem;
        font-weight: 600;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        backdrop-filter: blur(10px);
    }

    .mega-stat-trend.up {
        background: rgba(46, 204, 113, 0.2);
        color: #27ae60;
        border: 1px solid rgba(46, 204, 113, 0.4);
    }

    .mega-stat-trend.down {
        background: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
        border: 1px solid rgba(231, 76, 60, 0.4);
    }

    .mega-stat-value {
        font-size: 2.5rem;
        font-weight: 900;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e, #ffa8a8);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        animation: valueShimmer 2s ease-in-out infinite;
    }

    @keyframes valueShimmer {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }

    .mega-stat-label {
        color: #2c3e50;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .mega-stat-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid rgba(255, 182, 193, 0.3);
    }

    .mega-stat-detail {
        text-align: center;
    }

    .mega-stat-detail-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
    }

    .mega-stat-detail-label {
        font-size: 0.8rem;
        color: #7f8c8d;
        margin-top: 0.2rem;
    }

    /* Circular Progress Stats */
    .circular-stat {
        position: relative;
        width: 120px;
        height: 120px;
        margin: 0 auto 1rem;
    }

    .circular-progress {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: conic-gradient(
            from 0deg,
            #ff6b6b 0deg,
            #ff8e8e var(--progress, 0deg),
            rgba(255, 182, 193, 0.3) var(--progress, 0deg)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .circular-progress::before {
        content: '';
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    .circular-progress-value {
        position: absolute;
        font-size: 1.2rem;
        font-weight: 900;
        color: #2c3e50;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }

    /* Real-time Data Animation */
    .real-time-stats {
        margin-top: 3rem;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        border: 1px solid rgba(255, 182, 193, 0.3);
    }

    .real-time-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .real-time-card {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(255, 240, 245, 0.8) 100%);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem;
        border: 1px solid rgba(255, 182, 193, 0.3);
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .real-time-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #ff6b6b, #ff8e8e, #ffa8a8);
        animation: progressBar 3s ease-in-out infinite;
    }

    @keyframes progressBar {
        0%, 100% { transform: translateX(-100%); }
        50% { transform: translateX(100%); }
    }

    .real-time-value {
        font-size: 2rem;
        font-weight: 900;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    .real-time-label {
        color: #2c3e50;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .real-time-trend {
        margin-top: 0.5rem;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .trend-up {
        color: #27ae60;
    }

    .trend-down {
        color: #e74c3c;
    }

    /* Floating Elements */
    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    .floating-element {
        position: absolute;
        width: 10px;
        height: 10px;
        background: rgba(255, 182, 193, 0.4);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    .floating-element:nth-child(1) { left: 10%; animation-delay: 0s; }
    .floating-element:nth-child(2) { left: 20%; animation-delay: 1s; }
    .floating-element:nth-child(3) { left: 30%; animation-delay: 2s; }
    .floating-element:nth-child(4) { left: 40%; animation-delay: 3s; }
    .floating-element:nth-child(5) { left: 50%; animation-delay: 4s; }

    @keyframes float {
        0%, 100% {
            transform: translateY(100vh) scale(0);
            opacity: 0;
        }
        10% {
            opacity: 1;
            transform: scale(1);
        }
        90% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            transform: translateY(-100px) scale(0);
            opacity: 0;
        }
    }

    /* Glowing Effects */
    .glow-effect {
        position: relative;
    }

    .glow-effect::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #ff6b6b, #ff8e8e, #ffa8a8, #ff6b6b);
        border-radius: inherit;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
        animation: rotate 4s linear infinite;
    }

    .glow-effect:hover::after {
        opacity: 0.7;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .action-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.4s ease;
        text-decoration: none;
        color: inherit;
        display: block;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient);
        transition: height 0.3s ease;
    }

    .action-card:hover {
        background: rgba(255, 255, 255, 1);
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .action-card:hover::before {
        height: 8px;
    }

    .action-icon {
        width: 70px;
        height: 70px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        margin-bottom: 1.5rem;
        background: var(--gradient);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .action-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    .action-desc {
        color: #6c757d;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Action card gradients */
    .action-add { --gradient: linear-gradient(135deg, #28a745, #20c997); }
    .action-remove { --gradient: linear-gradient(135deg, #dc3545, #fd7e14); }
    .action-transfer { --gradient: linear-gradient(135deg, #007bff, #6610f2); }
    .action-count { --gradient: linear-gradient(135deg, #ffc107, #fd7e14); }
    .action-report { --gradient: linear-gradient(135deg, #6f42c1, #e83e8c); }
    .action-alert { --gradient: linear-gradient(135deg, #dc3545, #fd7e14); }
    .action-adjust { --gradient: linear-gradient(135deg, #17a2b8, #6f42c1); }
    .action-locations { --gradient: linear-gradient(135deg, #28a745, #17a2b8); }

    /* Quick Actions Dropdown Styles */
    .quick-actions-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 20px;
        font-weight: 600;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-actions-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .quick-actions-btn:hover::before {
        left: 100%;
    }

    .quick-actions-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    }







    @keyframes dropdownFadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .quick-action-item {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        border: none;
        background: transparent;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #333;
        position: relative;
        overflow: hidden;
    }

    .quick-action-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.3s ease;
    }

    .quick-action-item:hover::before {
        left: 100%;
    }

    .quick-action-item:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        transform: translateX(5px);
        color: #667eea;
    }

    .quick-action-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.2rem;
        color: white;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }

    .quick-action-item:hover .quick-action-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .quick-action-content {
        flex: 1;
    }

    .quick-action-title {
        font-weight: 600;
        font-size: 0.95rem;
        margin-bottom: 2px;
        color: #333;
        transition: color 0.3s ease;
    }

    .quick-action-item:hover .quick-action-title {
        color: #667eea;
    }

    .quick-action-desc {
        font-size: 0.8rem;
        color: #666;
        line-height: 1.3;
    }

    .quick-action-badge {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        border-radius: 12px;
        padding: 4px 8px;
        font-size: 0.7rem;
        font-weight: 600;
        min-width: 20px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    /* Background colors for icons */
    .bg-purple { background: linear-gradient(135deg, #6f42c1, #e83e8c); }
    .bg-orange { background: linear-gradient(135deg, #fd7e14, #ffc107); }

    /* إجبار القائمة على الظهور فوق كل عنصر ممكن */
    .dropdown.show .quick-actions-menu,
    .dropdown-menu.quick-actions-menu.show,
    .quick-actions-menu.show {
        z-index: 2147483647 !important;
        position: fixed !important;
        transform: none !important;
    }



    /* زر المخزون الجميل */
    .inventory-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 20px;
        font-weight: 600;
        color: white !important;
        text-decoration: none !important;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
    }

    .inventory-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .inventory-btn:hover::before {
        left: 100%;
    }

    .inventory-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
        color: white !important;
        text-decoration: none !important;
    }

    .inventory-btn i {
        font-size: 1.1rem;
    }

    .inventory-badge {
        background: rgba(255, 255, 255, 0.9);
        color: #28a745;
        border-radius: 12px;
        padding: 4px 8px;
        font-size: 0.75rem;
        font-weight: 700;
        margin-left: 8px;
        min-width: 24px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }


</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // قائمة الإجراءات السريعة تعمل بـ Bootstrap العادي
    // القائمة الجانبية لها الأولوية دائماً

    // إغلاق قائمة الإجراءات السريعة عند فتح القائمة الجانبية
    const sidebarToggle = document.querySelector('[data-bs-toggle="offcanvas"]');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            // إغلاق قائمة الإجراءات السريعة إذا كانت مفتوحة
            const openDropdown = document.querySelector('.dropdown-menu.show');
            if (openDropdown) {
                const dropdownToggle = document.querySelector('[data-bs-toggle="dropdown"][aria-expanded="true"]');
                if (dropdownToggle) {
                    const dropdown = new bootstrap.Dropdown(dropdownToggle);
                    dropdown.hide();
                }
            }
        });
    }

    // إغلاق قائمة الإجراءات السريعة عند النقر خارجها
    document.addEventListener('click', function(e) {
        const quickActionsDropdown = document.getElementById('quickActionsDropdown');
        const dropdownMenu = document.querySelector('.quick-actions-menu');

        if (quickActionsDropdown && dropdownMenu &&
            !quickActionsDropdown.contains(e.target) &&
            !dropdownMenu.contains(e.target)) {

            const dropdown = new bootstrap.Dropdown(quickActionsDropdown);
            dropdown.hide();
        }
    });
});
</script>

<style>
    .content-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 40px rgba(31, 38, 135, 0.37);
    }

    .section-title {
        font-size: 1.8rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .alert-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* أزرار الإجراءات السريعة - في صف واحد */
    .quick-actions-buttons {
        display: flex;
        gap: 6px;
        align-items: center;
        flex-wrap: wrap;
        justify-content: flex-end;
    }

    .action-btn {
        border: none;
        border-radius: 8px;
        padding: 8px 12px;
        font-weight: 600;
        color: white !important;
        text-decoration: none !important;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 0.85rem;
        white-space: nowrap;
        min-width: 80px;
        height: 36px;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        color: white !important;
        text-decoration: none !important;
    }

    /* ألوان الأزرار */
    .receive-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .receive-btn:hover {
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    }

    .issue-btn {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .issue-btn:hover {
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
    }

    .transfer-btn {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .transfer-btn:hover {
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
    }

    .reports-btn {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
    }

    .reports-btn:hover {
        box-shadow: 0 8px 25px rgba(111, 66, 193, 0.4);
    }

    .count-btn {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }

    .count-btn:hover {
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    }

    .adjust-btn {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .adjust-btn:hover {
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
    }

    .alerts-btn {
        background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
        box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
        position: relative;
    }

    .alerts-btn:hover {
        box-shadow: 0 8px 25px rgba(253, 126, 20, 0.4);
    }

    .locations-btn {
        background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    }

    .locations-btn:hover {
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
    }

    /* شارة التنبيهات */
    .alert-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s infinite;
    }

    /* تحسين responsive للأزرار */
    @media (max-width: 1400px) {
        .action-btn span {
            display: none !important;
        }

        .action-btn {
            padding: 6px 8px;
            min-width: 36px;
        }
    }

    @media (max-width: 768px) {
        .quick-actions-buttons {
            gap: 2px;
        }

        .action-btn {
            padding: 5px 6px;
            font-size: 0.75rem;
            min-width: 32px;
        }

        .warehouse-header {
            padding: 1rem;
        }
    }

    /* Digital Warehouse Cards */
    .warehouse-digital-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 0;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        transition: all 0.4s ease;
        overflow: hidden;
        position: relative;
    }

    .warehouse-digital-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 70px rgba(31, 38, 135, 0.5);
    }

    .warehouse-digital-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .warehouse-header {
        padding: 2rem 2rem 1rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    }

    .warehouse-name {
        font-size: 1.4rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .warehouse-code {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .warehouse-type {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .status-indicator {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .status-active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        animation: pulse 2s infinite;
    }

    .status-inactive {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }

    .status-text {
        font-size: 0.8rem;
        font-weight: 600;
        text-align: center;
        display: block;
    }

    .warehouse-metrics {
        padding: 1rem 2rem;
    }

    .metric-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .metric-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .metric-item:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .metric-icon {
        width: 50px;
        height: 50px;
        border-radius: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .value-icon {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .metric-value {
        font-size: 1.8rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
    }

    .metric-value.currency {
        position: relative;
    }

    .metric-value.currency::after {
        content: " ج.م";
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 600;
    }

    /* Dynamic Status Colors */
    .warehouse-digital-card.high-value::before {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .warehouse-digital-card.medium-value::before {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
    }

    .warehouse-digital-card.low-value::before {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
    }

    /* Animated Numbers */
    .metric-value, .smart-value, .summary-value {
        animation: countUp 1s ease-out;
    }

    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Glowing Effect for High Value Items */
    .warehouse-digital-card.high-value {
        box-shadow: 0 15px 50px rgba(40, 167, 69, 0.3);
    }

    .warehouse-digital-card.high-value:hover {
        box-shadow: 0 25px 70px rgba(40, 167, 69, 0.5);
    }

    /* Special Effects for Large Numbers */
    .metric-value.large-number {
        text-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        animation: glow 2s ease-in-out infinite alternate;
    }

    @keyframes glow {
        from { text-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
        to { text-shadow: 0 0 30px rgba(102, 126, 234, 0.6); }
    }

    /* Status Indicators */
    .status-indicator.pulse {
        animation: statusPulse 1.5s ease-in-out infinite;
    }

    @keyframes statusPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .metric-label {
        color: #6c757d;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .smart-metrics {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .smart-metric {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 12px;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .smart-metric-icon {
        width: 35px;
        height: 35px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .smart-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #667eea;
        display: block;
        line-height: 1;
    }

    .smart-label {
        font-size: 0.75rem;
        color: #6c757d;
        font-weight: 500;
    }

    .capacity-indicator {
        margin-bottom: 1.5rem;
    }

    .capacity-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .capacity-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
    }

    .capacity-percentage {
        font-size: 0.9rem;
        font-weight: 700;
        color: #667eea;
    }

    .capacity-bar {
        height: 8px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
    }

    .capacity-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 10px;
        transition: width 0.5s ease;
    }

    .warehouse-actions {
        padding: 1rem 2rem;
        background: rgba(248, 249, 250, 0.5);
    }

    .action-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .action-btn.primary {
        background: linear-gradient(135deg, #007bff, #6610f2);
        color: white;
    }

    .action-btn.success {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .quick-stats {
        display: flex;
        justify-content: space-around;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
    }

    .quick-stat {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        font-weight: 600;
        color: #6c757d;
    }

    .warehouse-footer {
        padding: 1rem 2rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid rgba(102, 126, 234, 0.1);
    }

    .footer-info {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        align-items: center;
    }

    .footer-actions {
        display: flex;
        gap: 0.5rem;
    }

    .footer-btn {
        width: 35px;
        height: 35px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .footer-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .warehouses-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
        padding: 2rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        border-radius: 20px;
    }

    .summary-card {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    }

    .summary-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .summary-value {
        font-size: 2rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
    }

    .summary-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .btn-warehouse {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-warehouse:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.5);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Warehouse Header -->
<div class="warehouse-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-4">
                <h3 class="mb-1">
                    <i class="bi bi-building me-2"></i>
                    {% if user_language == 'en' %}Warehouse Dashboard{% else %}لوحة تحكم المخازن{% endif %}
                </h3>
                <p class="mb-0 opacity-75 small">{% if user_language == 'en' %}Inventory management{% else %}إدارة المخزون{% endif %}</p>
            </div>
            <div class="col-md-8 text-end">
                <div class="d-flex align-items-center justify-content-end flex-wrap">
                    <!-- أزرار الإجراءات السريعة -->
                    <div class="quick-actions-buttons d-flex align-items-center me-2">
                        <!-- زر إضافة مخزون -->
                        <a href="{% url 'warehouses:receive_history' %}" class="btn action-btn receive-btn" title="سجل الإضافات وإضافة مخزون جديد">
                            <i class="bi bi-plus-circle me-1"></i>إضافة
                        </a>

                        <!-- زر صرف مخزون -->
                        <a href="{% url 'warehouses:issue_history' %}" class="btn action-btn issue-btn" title="سجل الصرف وإصدار أذن صرف جديد">
                            <i class="bi bi-dash-circle me-1"></i>صرف
                        </a>

                        <!-- زر تحويل مخزون -->
                        <a href="{% url 'warehouses:transfer_history' %}" class="btn action-btn transfer-btn" title="سجل التحويلات وإجراء تحويل جديد">
                            <i class="bi bi-arrow-left-right me-1"></i>تحويل
                        </a>

                        <!-- زر جرد المخزون -->
                        <a href="{% url 'warehouses:count_history' %}" class="btn action-btn count-btn" title="سجل الجرد وإجراء جرد جديد">
                            <i class="bi bi-clipboard-check me-1"></i>جرد
                        </a>

                        <!-- زر التقارير -->
                        <a href="{% url 'warehouses:stock_reports' %}" class="btn action-btn reports-btn" title="التقارير">
                            <i class="bi bi-graph-up me-1"></i>تقارير
                        </a>

                        <!-- زر تسويات المخزون -->
                        <a href="{% url 'warehouses:adjustments_history' %}" class="btn action-btn adjust-btn" title="سجل التسويات وإجراء تسوية جديدة">
                            <i class="bi bi-gear me-1"></i>تسويات
                        </a>

                        <!-- زر التنبيهات -->
                        <a href="{% url 'warehouses:low_stock_alerts' %}" class="btn action-btn alerts-btn" title="التنبيهات">
                            <i class="bi bi-exclamation-triangle me-1"></i>تنبيهات
                            {% if low_stock_items > 0 or out_of_stock_items > 0 %}
                            <span class="alert-badge">{{ low_stock_items|add:out_of_stock_items }}</span>
                            {% endif %}
                        </a>

                        <!-- زر أماكن المخازن -->
                        <a href="{% url 'definitions:warehouse_location_list' %}" class="btn action-btn locations-btn" title="أماكن المخازن">
                            <i class="bi bi-geo-alt me-1"></i>أماكن
                        </a>
                    </div>

                    <!-- زر المخزون الجميل -->
                    <a href="{% url 'warehouses:inventory_list' %}" class="btn inventory-btn me-2">
                        <i class="bi bi-boxes me-2"></i>
                        <span>{% if user_language == 'en' %}Inventory{% else %}المخزون{% endif %}</span>
                        <div class="inventory-badge">{{ total_inventory_items|default:0 }}</div>
                    </a>


                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Advanced Warehouses Overview -->
    {% if warehouses %}
    <div class="content-section">
        <h2 class="section-title">
            <i class="bi bi-building"></i>
            {% if user_language == 'en' %}Digital Warehouses Overview{% else %}نظرة عامة رقمية على المخازن{% endif %}
        </h2>

        <div class="row">
            {% for warehouse in warehouses %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="warehouse-digital-card
                    {% if warehouse.warehouse_total_value > 100000 %}high-value
                    {% elif warehouse.warehouse_total_value > 50000 %}medium-value
                    {% else %}low-value{% endif %}">
                    <!-- Header with Status -->
                    <div class="warehouse-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="warehouse-info">
                                <h5 class="warehouse-name">{{ warehouse.name }}</h5>
                                <span class="warehouse-code">{{ warehouse.code }}</span>
                                <div class="warehouse-type">
                                    <i class="bi bi-{% if warehouse.warehouse_type == 'main' %}building-fill{% elif warehouse.warehouse_type == 'branch' %}shop{% elif warehouse.warehouse_type == 'raw_materials' %}boxes{% else %}building{% endif %} me-1"></i>
                                    {{ warehouse.get_warehouse_type_display }}
                                </div>
                            </div>
                            <div class="warehouse-status">
                                <div class="status-indicator {% if warehouse.is_active %}status-active pulse{% else %}status-inactive{% endif %}">
                                    <i class="bi bi-{% if warehouse.is_active %}check-circle{% else %}x-circle{% endif %}"></i>
                                </div>
                                <small class="status-text">{% if warehouse.is_active %}نشط{% else %}غير نشط{% endif %}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Digital Metrics -->
                    <div class="warehouse-metrics">
                        <div class="metric-row">
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="bi bi-boxes"></i>
                                </div>
                                <div class="metric-data">
                                    <div class="metric-value">{{ warehouse.items_count|default:0 }}</div>
                                    <div class="metric-label">عنصر مخزون</div>
                                </div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-icon value-icon">
                                    <i class="bi bi-currency-exchange"></i>
                                </div>
                                <div class="metric-data">
                                    <div class="metric-value currency {% if warehouse.warehouse_total_value > 100000 %}large-number{% endif %}">
                                        {{ warehouse.warehouse_total_value|number_format }}
                                    </div>
                                    <div class="metric-label">جنيه مصري</div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Smart Metrics -->
                        <div class="smart-metrics">
                            <div class="smart-metric">
                                <div class="smart-metric-icon">
                                    <i class="bi bi-speedometer2"></i>
                                </div>
                                <div class="smart-metric-data">
                                    <span class="smart-value">
                                        {{ warehouse.warehouse_total_value|div:warehouse.items_count|floatformat:1 }}
                                    </span>
                                    <span class="smart-label">متوسط قيمة العنصر</span>
                                </div>
                            </div>

                            <div class="smart-metric">
                                <div class="smart-metric-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div class="smart-metric-data">
                                    <span class="smart-value">
                                        {{ warehouse.warehouse_total_value|percentage:total_stock_value }}%
                                    </span>
                                    <span class="smart-label">من إجمالي القيمة</span>
                                </div>
                            </div>
                        </div>

                        <!-- Capacity Indicator -->
                        <div class="capacity-indicator">
                            <div class="capacity-header">
                                <span class="capacity-label">كثافة المخزون</span>
                                <span class="capacity-percentage">
                                    {{ warehouse.items_count|percentage:50 }}%
                                </span>
                            </div>
                            <div class="capacity-bar">
                                <div class="capacity-fill" style="width: {{ warehouse.items_count|percentage:50 }}%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="warehouse-actions">
                        <div class="action-buttons">
                            <a href="{% url 'warehouses:inventory_list' %}?warehouse={{ warehouse.id }}" class="action-btn primary">
                                <i class="bi bi-eye"></i>
                                <span>عرض المخزون</span>
                            </a>
                            <a href="{% url 'warehouses:stock_receive' %}?warehouse={{ warehouse.id }}" class="action-btn success">
                                <i class="bi bi-plus-circle"></i>
                                <span>إضافة مخزون</span>
                            </a>
                        </div>

                        <div class="quick-stats">
                            <div class="quick-stat {% if warehouse.low_stock_count > 0 %}text-warning{% endif %}">
                                <i class="bi bi-exclamation-triangle"></i>
                                <span>منخفض: {{ warehouse.low_stock_count|default:0 }}</span>
                            </div>
                            <div class="quick-stat {% if warehouse.out_of_stock_count > 0 %}text-danger{% endif %}">
                                <i class="bi bi-x-circle"></i>
                                <span>نافد: {{ warehouse.out_of_stock_count|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Digital Footer -->
                    <div class="warehouse-footer">
                        <div class="footer-info">
                            <i class="bi bi-geo-alt me-1"></i>
                            <span>{{ warehouse.address|default:"غير محدد"|truncatechars:30 }}</span>
                        </div>
                        <div class="footer-actions">
                            <a href="{% url 'definitions:warehouse_edit' warehouse.id %}" class="footer-btn" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="{% url 'warehouses:stock_reports' %}?warehouse={{ warehouse.id }}" class="footer-btn" title="تقارير">
                                <i class="bi bi-graph-up"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Summary Statistics -->
        <div class="warehouses-summary">
            <div class="summary-card">
                <div class="summary-icon">
                    <i class="bi bi-building"></i>
                </div>
                <div class="summary-data">
                    <div class="summary-value">{{ total_warehouses }}</div>
                    <div class="summary-label">إجمالي المخازن</div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-icon">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="summary-data">
                    <div class="summary-value">{{ total_stock_value|number_format }}</div>
                    <div class="summary-label">إجمالي القيمة (ج.م)</div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-icon">
                    <i class="bi bi-boxes"></i>
                </div>
                <div class="summary-data">
                    <div class="summary-value">{{ total_inventory_items }}</div>
                    <div class="summary-label">إجمالي العناصر</div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-icon">
                    <i class="bi bi-graph-up"></i>
                </div>
                <div class="summary-data">
                    <div class="summary-value">{{ total_stock_value|div:total_inventory_items|floatformat:1 }}</div>
                    <div class="summary-label">متوسط قيمة العنصر (ج.م)</div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-warehouse">
                <i class="bi bi-building me-2"></i>إدارة جميع المخازن
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stats-card">
            <i class="bi bi-building stat-icon"></i>
            <div class="stat-number">{{ total_warehouses }}</div>
            <div class="stat-label">{% if user_language == 'en' %}Active Warehouses{% else %}المخازن النشطة{% endif %}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i> +2 هذا الشهر
            </div>
        </div>

        <div class="stats-card">
            <i class="bi bi-box-seam stat-icon"></i>
            <div class="stat-number">{{ total_inventory_items|default:0 }}</div>
            <div class="stat-label">{% if user_language == 'en' %}Inventory Items{% else %}عناصر المخزون{% endif %}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i> +15 هذا الأسبوع
            </div>
        </div>

        <div class="stats-card">
            <i class="bi bi-currency-dollar stat-icon"></i>
            <div class="stat-number">{{ total_stock_value|floatformat:1|default:0 }} ج.م</div>
            <div class="stat-label">{% if user_language == 'en' %}Total Stock Value{% else %}إجمالي قيمة المخزون{% endif %}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i> +5.2% هذا الشهر
            </div>
        </div>

        <div class="stats-card">
            <i class="bi bi-exclamation-triangle stat-icon"></i>
            <div class="stat-number">{{ low_stock_items|default:0 }}</div>
            <div class="stat-label">{% if user_language == 'en' %}Low Stock Items{% else %}مخزون منخفض{% endif %}</div>
            {% if low_stock_items > 0 %}
            <div class="stat-change negative">
                <i class="bi bi-arrow-down"></i> يحتاج انتباه
            </div>
            {% endif %}
        </div>

        <div class="stats-card">
            <i class="bi bi-x-circle stat-icon"></i>
            <div class="stat-number">{{ out_of_stock_items|default:0 }}</div>
            <div class="stat-label">{% if user_language == 'en' %}Out of Stock{% else %}مخزون نافد{% endif %}</div>
            {% if out_of_stock_items > 0 %}
            <div class="stat-change negative">
                <i class="bi bi-exclamation-triangle"></i> عاجل
            </div>
            {% endif %}
        </div>

        <div class="stats-card">
            <i class="bi bi-arrow-left-right stat-icon"></i>
            <div class="stat-number">{{ today_transactions|default:0 }}</div>
            <div class="stat-label">{% if user_language == 'en' %}Today's Transactions{% else %}حركات اليوم{% endif %}</div>
            <div class="stat-change positive">
                <i class="bi bi-clock"></i> آخر تحديث: الآن
            </div>
        </div>
    </div>

    <!-- Advanced Statistics Section -->
    <div class="advanced-stats-section">
        <h2 class="section-title text-center mb-4" style="color: #2c3e50; font-size: 2.5rem; font-weight: 800; text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);">
            <i class="bi bi-graph-up-arrow me-3"></i>
            {% if user_language == 'en' %}Advanced Analytics{% else %}التحليلات المتقدمة{% endif %}
        </h2>

        <div class="stats-mega-grid">
            <!-- Warehouse Performance -->
            <div class="mega-stat-card">
                <div class="mega-stat-header">
                    <div class="mega-stat-icon">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <div class="mega-stat-trend up">
                        <i class="bi bi-arrow-up"></i> +12.5%
                    </div>
                </div>
                <div class="mega-stat-value">{{ total_warehouses|default:0 }}</div>
                <div class="mega-stat-label">أداء المخازن</div>
                <div class="mega-stat-details">
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">{{ total_warehouses|default:0 }}</div>
                        <div class="mega-stat-detail-label">نشط</div>
                    </div>
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">0</div>
                        <div class="mega-stat-detail-label">معطل</div>
                    </div>
                </div>
            </div>

            <!-- Inventory Turnover -->
            <div class="mega-stat-card">
                <div class="mega-stat-header">
                    <div class="mega-stat-icon">
                        <i class="bi bi-arrow-repeat"></i>
                    </div>
                    <div class="mega-stat-trend up">
                        <i class="bi bi-arrow-up"></i> +8.3%
                    </div>
                </div>
                <div class="mega-stat-value">{{ today_transactions|default:0 }}</div>
                <div class="mega-stat-label">دوران المخزون</div>
                <div class="mega-stat-details">
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">{{ today_transactions|default:0 }}</div>
                        <div class="mega-stat-detail-label">اليوم</div>
                    </div>
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">{{ week_transactions|default:0 }}</div>
                        <div class="mega-stat-detail-label">الأسبوع</div>
                    </div>
                </div>
            </div>

            <!-- Stock Value Analysis -->
            <div class="mega-stat-card">
                <div class="mega-stat-header">
                    <div class="mega-stat-icon">
                        <i class="bi bi-currency-exchange"></i>
                    </div>
                    <div class="mega-stat-trend up">
                        <i class="bi bi-arrow-up"></i> +15.7%
                    </div>
                </div>
                <div class="mega-stat-value">{{ total_stock_value|floatformat:0|default:0 }}</div>
                <div class="mega-stat-label">قيمة المخزون (ج.م)</div>
                <div class="mega-stat-details">
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">{{ total_inventory_items|default:0 }}</div>
                        <div class="mega-stat-detail-label">عنصر</div>
                    </div>
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">{{ total_stock_value|div:total_inventory_items|floatformat:1|default:0 }}</div>
                        <div class="mega-stat-detail-label">متوسط</div>
                    </div>
                </div>
            </div>

            <!-- Alert Status -->
            <div class="mega-stat-card">
                <div class="mega-stat-header">
                    <div class="mega-stat-icon">
                        <i class="bi bi-shield-exclamation"></i>
                    </div>
                    <div class="mega-stat-trend {% if low_stock_items > 0 or out_of_stock_items > 0 %}down{% else %}up{% endif %}">
                        {% if low_stock_items > 0 or out_of_stock_items > 0 %}
                            <i class="bi bi-arrow-down"></i> تحذير
                        {% else %}
                            <i class="bi bi-check-circle"></i> آمن
                        {% endif %}
                    </div>
                </div>
                <div class="mega-stat-value">{{ low_stock_items|add:out_of_stock_items|default:0 }}</div>
                <div class="mega-stat-label">تنبيهات المخزون</div>
                <div class="mega-stat-details">
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">{{ low_stock_items|default:0 }}</div>
                        <div class="mega-stat-detail-label">منخفض</div>
                    </div>
                    <div class="mega-stat-detail">
                        <div class="mega-stat-detail-value">{{ out_of_stock_items|default:0 }}</div>
                        <div class="mega-stat-detail-label">نافد</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Circular Progress Stats -->
        <div class="row mt-5">
            <div class="col-md-4 text-center">
                <div class="circular-stat">
                    <div class="circular-progress" style="--progress: {{ total_warehouses|to_degrees:10 }}deg;">
                        <div class="circular-progress-value">{{ total_warehouses|safe_percentage:10 }}%</div>
                    </div>
                </div>
                <h5 style="color: #2c3e50; margin-top: 1rem; font-weight: 700;">كفاءة المخازن</h5>
                <p style="color: #7f8c8d;">نسبة الاستخدام الأمثل</p>
            </div>

            <div class="col-md-4 text-center">
                <div class="circular-stat">
                    <div class="circular-progress" style="--progress: {{ today_transactions|to_degrees:50 }}deg;">
                        <div class="circular-progress-value">{{ today_transactions|safe_percentage:50 }}%</div>
                    </div>
                </div>
                <h5 style="color: #2c3e50; margin-top: 1rem; font-weight: 700;">نشاط اليوم</h5>
                <p style="color: #7f8c8d;">مقارنة بالمتوسط</p>
            </div>

            <div class="col-md-4 text-center">
                <div class="circular-stat">
                    <div class="circular-progress" style="--progress: {{ total_inventory_items|to_degrees:1000 }}deg;">
                        <div class="circular-progress-value">{{ total_inventory_items|safe_percentage:1000 }}%</div>
                    </div>
                </div>
                <h5 style="color: #2c3e50; margin-top: 1rem; font-weight: 700;">امتلاء المخزون</h5>
                <p style="color: #7f8c8d;">نسبة الامتلاء الحالية</p>
            </div>
        </div>

        <!-- Real-time Statistics -->
        <div class="real-time-stats">
            <h3 class="text-center mb-4" style="color: #2c3e50; font-weight: 800; text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);">
                <i class="bi bi-activity me-2"></i>
                {% if user_language == 'en' %}Real-time Metrics{% else %}المقاييس الفورية{% endif %}
            </h3>

            <div class="real-time-grid">
                <div class="real-time-card glow-effect">
                    <div class="real-time-value">{{ total_warehouses|default:0 }}</div>
                    <div class="real-time-label">مخازن نشطة</div>
                    <div class="real-time-trend trend-up">
                        <i class="bi bi-arrow-up"></i> +2 هذا الشهر
                    </div>
                </div>

                <div class="real-time-card glow-effect">
                    <div class="real-time-value">{{ today_transactions|default:0 }}</div>
                    <div class="real-time-label">حركات اليوم</div>
                    <div class="real-time-trend trend-up">
                        <i class="bi bi-arrow-up"></i> +{{ today_transactions|default:0 }}
                    </div>
                </div>

                <div class="real-time-card glow-effect">
                    <div class="real-time-value">{{ total_inventory_items|default:0 }}</div>
                    <div class="real-time-label">عناصر المخزون</div>
                    <div class="real-time-trend trend-up">
                        <i class="bi bi-arrow-up"></i> +15 هذا الأسبوع
                    </div>
                </div>

                <div class="real-time-card glow-effect">
                    <div class="real-time-value">{{ total_stock_value|floatformat:0|default:0 }}</div>
                    <div class="real-time-label">قيمة إجمالية (ج.م)</div>
                    <div class="real-time-trend trend-up">
                        <i class="bi bi-arrow-up"></i> +5.2%
                    </div>
                </div>

                <div class="real-time-card glow-effect">
                    <div class="real-time-value">{{ low_stock_items|add:out_of_stock_items|default:0 }}</div>
                    <div class="real-time-label">تنبيهات نشطة</div>
                    {% if low_stock_items > 0 or out_of_stock_items > 0 %}
                    <div class="real-time-trend trend-down">
                        <i class="bi bi-exclamation-triangle"></i> يحتاج انتباه
                    </div>
                    {% else %}
                    <div class="real-time-trend trend-up">
                        <i class="bi bi-check-circle"></i> كل شيء بخير
                    </div>
                    {% endif %}
                </div>

                <div class="real-time-card glow-effect">
                    <div class="real-time-value">{{ total_stock_value|div:total_inventory_items|floatformat:1|default:0 }}</div>
                    <div class="real-time-label">متوسط قيمة العنصر</div>
                    <div class="real-time-trend trend-up">
                        <i class="bi bi-graph-up"></i> محسوب تلقائياً
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Background Elements -->
    <div class="floating-elements">
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
    </div>

</div>
{% endblock %}
