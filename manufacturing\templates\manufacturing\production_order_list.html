{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-clipboard-data me-3"></i>
                    أوامر الإنتاج
                </h1>
                <p class="text-white-50 mb-0 mt-2">إدارة ومتابعة جميع أوامر الإنتاج</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:production_order_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>أمر إنتاج جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الأمر</th>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in page_obj %}
                            <tr>
                                <td>{{ order.order_number }}</td>
                                <td>{{ order.product.name }}</td>
                                <td>{{ order.quantity }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ order.get_status_display }}</span>
                                </td>
                                <td>{{ order.created_at|date:"Y/m/d" }}</td>
                                <td>
                                    <a href="{% url 'manufacturing:production_order_detail' order.pk %}" class="btn btn-sm btn-outline-primary">
                                        عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5" style="background: rgba(255, 255, 255, 0.9); border-radius: 20px;">
                    <i class="bi bi-clipboard-x" style="font-size: 4rem; color: #667eea;"></i>
                    <h4 class="mt-3" style="color: #2c3e50;">لا توجد أوامر إنتاج</h4>
                    <p style="color: #6c757d;">لم يتم إنشاء أي أوامر إنتاج بعد. ابدأ بإنشاء أول أمر إنتاج.</p>
                    <a href="{% url 'manufacturing:production_order_create' %}" class="btn btn-primary btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>إنشاء أمر إنتاج جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- محتوى إضافي مؤقت -->
    <div class="text-center mt-4" style="background: rgba(255, 255, 255, 0.9); padding: 2rem; border-radius: 20px;">
        <h5 style="color: #2c3e50;">قريباً: ميزات متقدمة</h5>
        <p style="color: #6c757d;">
            • فلترة متقدمة للأوامر<br>
            • تتبع مراحل الإنتاج<br>
            • تقارير الأداء<br>
            • إشعارات فورية
        </p>
    </div>
</div>
{% endblock %}
