{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .schedule-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .schedule-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .schedule-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .schedule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(155, 89, 182, 0.1);
    }

    .schedule-title {
        font-size: 1.3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .schedule-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;
        padding: 0.5rem;
        background: rgba(155, 89, 182, 0.05);
        border-radius: 10px;
    }

    .schedule-info-label {
        font-weight: 600;
        color: #495057;
    }

    .schedule-info-value {
        font-weight: 500;
        color: #2c3e50;
    }

    .frequency-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .frequency-daily { background: #e74c3c; color: white; }
    .frequency-weekly { background: #f39c12; color: white; }
    .frequency-monthly { background: #3498db; color: white; }
    .frequency-quarterly { background: #9b59b6; color: white; }
    .frequency-yearly { background: #27ae60; color: white; }

    .due-indicator {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .due-overdue {
        background: #e74c3c;
        color: white;
        animation: pulse 2s infinite;
    }

    .due-soon {
        background: #f39c12;
        color: white;
    }

    .due-ok {
        background: #27ae60;
        color: white;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .next-maintenance {
        font-size: 1.1rem;
        font-weight: 700;
        color: #9b59b6;
        text-align: center;
        padding: 1rem;
        background: rgba(155, 89, 182, 0.1);
        border-radius: 10px;
        margin: 1rem 0;
    }

    .btn-group-custom {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .btn-custom {
        flex: 1;
        padding: 0.8rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        text-align: center;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(155, 89, 182, 0.3);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        margin: 2rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-calendar-check me-3"></i>
                    جدولة الصيانة
                </h1>
                <p class="text-white-50 mb-0 mt-2">إدارة ومتابعة جداول الصيانة الدورية والوقائية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:maintenance_schedule_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>جدولة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة جداول الصيانة -->
    {% if page_obj %}
    <div class="schedule-grid">
        {% for schedule in page_obj %}
        <div class="schedule-card">
            {% if schedule.is_overdue %}
            <div class="due-indicator due-overdue">
                <i class="bi bi-exclamation-triangle me-1"></i>متأخرة
            </div>
            {% elif schedule.days_until_due <= 7 %}
            <div class="due-indicator due-soon">
                <i class="bi bi-clock me-1"></i>قريباً
            </div>
            {% else %}
            <div class="due-indicator due-ok">
                <i class="bi bi-check-circle me-1"></i>في الموعد
            </div>
            {% endif %}
            
            <div class="schedule-header">
                <div class="schedule-title">{{ schedule.equipment.name }}</div>
                <div>
                    <span class="frequency-badge frequency-{{ schedule.frequency }}">
                        {{ schedule.get_frequency_display }}
                    </span>
                </div>
            </div>

            <div class="schedule-info">
                <div class="schedule-info-item">
                    <span class="schedule-info-label">نوع الصيانة:</span>
                    <span class="schedule-info-value">{{ schedule.maintenance_type.name }}</span>
                </div>
                <div class="schedule-info-item">
                    <span class="schedule-info-label">خط الإنتاج:</span>
                    <span class="schedule-info-value">{{ schedule.equipment.production_line.name }}</span>
                </div>
                <div class="schedule-info-item">
                    <span class="schedule-info-label">التكرار:</span>
                    <span class="schedule-info-value">كل {{ schedule.frequency_value }} {{ schedule.get_frequency_display }}</span>
                </div>
                <div class="schedule-info-item">
                    <span class="schedule-info-label">آخر صيانة:</span>
                    <span class="schedule-info-value">
                        {% if schedule.last_maintenance_date %}
                            {{ schedule.last_maintenance_date|date:"Y/m/d" }}
                        {% else %}
                            لم تتم بعد
                        {% endif %}
                    </span>
                </div>
                <div class="schedule-info-item">
                    <span class="schedule-info-label">المدة المقدرة:</span>
                    <span class="schedule-info-value">{{ schedule.maintenance_type.estimated_duration_hours }} ساعة</span>
                </div>
            </div>

            <div class="next-maintenance">
                <i class="bi bi-calendar-event me-2"></i>
                الصيانة القادمة: {{ schedule.next_maintenance_date|date:"Y/m/d" }}
                {% if schedule.days_until_due < 0 %}
                (متأخرة {{ schedule.days_until_due|abs }} يوم)
                {% elif schedule.days_until_due == 0 %}
                (اليوم)
                {% else %}
                (خلال {{ schedule.days_until_due }} يوم)
                {% endif %}
            </div>

            {% if schedule.notes %}
            <div class="mt-2">
                <small class="text-muted">
                    <strong>ملاحظات:</strong> {{ schedule.notes|truncatewords:15 }}
                </small>
            </div>
            {% endif %}

            <div class="btn-group-custom">
                <a href="#" class="btn-custom btn-primary-custom">
                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                </a>
                {% if schedule.days_until_due <= 0 %}
                <a href="{% url 'manufacturing:maintenance_record_create' %}?equipment={{ schedule.equipment.pk }}&type={{ schedule.maintenance_type.pk }}" 
                   class="btn-custom btn-outline-success">
                    <i class="bi bi-plus-circle me-2"></i>إنشاء سجل صيانة
                </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    {% else %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="bi bi-calendar-check" style="font-size: 4rem; color: #9b59b6;"></i>
        </div>
        <h3 style="color: #2c3e50;">لا توجد جداول صيانة</h3>
        <p style="color: #6c757d;">لم يتم إنشاء أي جداول صيانة بعد. ابدأ بإنشاء جدولة صيانة جديدة.</p>
        <a href="{% url 'manufacturing:maintenance_schedule_create' %}" class="btn btn-primary btn-lg">
            <i class="bi bi-plus-circle me-2"></i>إنشاء جدولة صيانة جديدة
        </a>
    </div>
    {% endif %}

    <!-- أزرار العودة -->
    <div class="text-center mt-4">
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                <i class="bi bi-house me-2"></i>لوحة التحكم
            </a>
            <a href="{% url 'manufacturing:maintenance_dashboard' %}" class="btn btn-outline-primary">
                <i class="bi bi-tools me-2"></i>لوحة تحكم الصيانة
            </a>
        </div>
    </div>
</div>
{% endblock %}
