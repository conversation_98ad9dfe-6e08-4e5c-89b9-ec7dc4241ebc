{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .detail-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #667eea;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .info-item {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 10px;
        padding: 1rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .info-value {
        font-weight: 500;
        color: #2c3e50;
        font-size: 1.1rem;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-box {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
    }

    .maintenance-alert {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .maintenance-alert i {
        font-size: 1.5rem;
    }

    .table-modern {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .table-modern thead {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .table-modern th {
        border: none;
        padding: 1rem;
        font-weight: 700;
        text-transform: uppercase;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }

    .table-modern td {
        border: none;
        padding: 1rem;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .table-modern tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .btn-action {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 0.8rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .condition-badge {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
    }

    .condition-excellent { background: #27ae60; color: white; }
    .condition-good { background: #2ecc71; color: white; }
    .condition-fair { background: #f39c12; color: white; }
    .condition-poor { background: #e67e22; color: white; }
    .condition-critical { background: #e74c3c; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {{ equipment.name }}
                </h1>
                <p class="text-white-50 mb-0 mt-2">تفاصيل المعدة - {{ equipment.code }}</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end flex-wrap">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:equipment_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-gear-wide-connected me-2"></i>المعدات
                    </a>
                    <a href="{% url 'manufacturing:equipment_update' equipment.pk %}" class="btn btn-light">
                        <i class="bi bi-pencil me-2"></i>تعديل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- تنبيه الصيانة -->
    {% if equipment.needs_maintenance %}
    <div class="maintenance-alert">
        <i class="bi bi-exclamation-triangle"></i>
        <div>
            <strong>تنبيه صيانة!</strong>
            <p class="mb-0">هذه المعدة تحتاج إلى صيانة. تاريخ الصيانة القادمة: {{ equipment.next_maintenance_date|date:"Y/m/d" }}</p>
        </div>
    </div>
    {% endif %}

    <!-- معلومات أساسية -->
    <div class="detail-section">
        <h3 class="section-title">
            <i class="bi bi-info-circle"></i>
            المعلومات الأساسية
        </h3>
        
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">اسم المعدة</div>
                <div class="info-value">{{ equipment.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الكود</div>
                <div class="info-value">{{ equipment.code }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الفئة</div>
                <div class="info-value">{{ equipment.category.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">خط الإنتاج</div>
                <div class="info-value">{{ equipment.production_line.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الشركة المصنعة</div>
                <div class="info-value">{{ equipment.manufacturer|default:"غير محدد" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الموديل</div>
                <div class="info-value">{{ equipment.model|default:"غير محدد" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الرقم التسلسلي</div>
                <div class="info-value">{{ equipment.serial_number|default:"غير محدد" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الحالة</div>
                <div class="info-value">
                    {% status_badge equipment.status "equipment" %}
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">الحالة الفنية</div>
                <div class="info-value">
                    <span class="condition-badge condition-{{ equipment.condition }}">
                        {{ equipment.get_condition_display }}
                    </span>
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">الموقع</div>
                <div class="info-value">{{ equipment.location|default:"غير محدد" }}</div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="detail-section">
        <h3 class="section-title">
            <i class="bi bi-graph-up"></i>
            إحصائيات سريعة
        </h3>
        
        <div class="stats-row">
            <div class="stat-box">
                <div class="stat-number">{{ total_maintenance_cost|format_currency }}</div>
                <div class="stat-label">إجمالي تكلفة الصيانة</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{{ total_downtime_hours|floatformat:1 }}</div>
                <div class="stat-label">ساعات التوقف الإجمالية</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{{ equipment.age_in_years|default:0|floatformat:1 }}</div>
                <div class="stat-label">عمر المعدة (سنة)</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{{ equipment.days_until_maintenance|default:0 }}</div>
                <div class="stat-label">أيام حتى الصيانة القادمة</div>
            </div>
        </div>
    </div>

    <!-- أحدث سجلات الصيانة -->
    {% if maintenance_records %}
    <div class="detail-section">
        <h3 class="section-title">
            <i class="bi bi-tools"></i>
            أحدث سجلات الصيانة
        </h3>
        
        <div class="table-modern">
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>نوع الصيانة</th>
                        <th>الحالة</th>
                        <th>المدة</th>
                        <th>التكلفة</th>
                        <th>الفني</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in maintenance_records %}
                    <tr>
                        <td>{{ record.scheduled_date|date:"Y/m/d" }}</td>
                        <td>{{ record.maintenance_type.name }}</td>
                        <td>
                            {% status_badge record.status "maintenance" %}
                        </td>
                        <td>{{ record.actual_duration_hours|default:record.estimated_duration_hours|time_format }}</td>
                        <td>{{ record.total_cost|format_currency }}</td>
                        <td>{{ record.performed_by.username|default:"غير محدد" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- أزرار الإجراءات -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="d-flex gap-2 justify-content-start">
                <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-house me-2"></i>لوحة التحكم
                </a>
                <a href="{% url 'manufacturing:equipment_list' %}" class="btn btn-outline-primary">
                    <i class="bi bi-gear-wide-connected me-2"></i>قائمة المعدات
                </a>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <div class="d-flex gap-2 justify-content-end">
                <a href="{% url 'manufacturing:maintenance_record_create' %}?equipment={{ equipment.pk }}" class="btn-action">
                    <i class="bi bi-plus-circle"></i>إضافة صيانة
                </a>
                <a href="#" class="btn-action">
                    <i class="bi bi-printer"></i>طباعة
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
