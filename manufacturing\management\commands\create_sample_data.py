from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta, date
from decimal import Decimal
import random

from manufacturing.models import (
    ProductionLine, Equipment, EquipmentCategory, MaintenanceType,
    MaintenanceSchedule, MaintenanceRecord, PerformanceMetric,
    DowntimeRecord, BillOfMaterials
)
from definitions.models import ProductDefinition, WarehouseDefinition


class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية لنظام التصنيع'

    def handle(self, *args, **options):
        self.stdout.write('بدء إنشاء البيانات التجريبية...')
        
        # إنشاء مستخدم تجريبي
        user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            user.set_password('admin123')
            user.save()
            self.stdout.write('تم إنشاء المستخدم التجريبي')

        # إنشاء مخازن تجريبية
        warehouse1, created = WarehouseDefinition.objects.get_or_create(
            name='مخزن الإنتاج الرئيسي',
            defaults={
                'code': 'MAIN-PROD',
                'description': 'المخزن الرئيسي للإنتاج',
                'is_active': True,
                'created_by': user
            }
        )

        warehouse2, created = WarehouseDefinition.objects.get_or_create(
            name='مخزن المواد الخام',
            defaults={
                'code': 'RAW-MAT',
                'description': 'مخزن المواد الخام',
                'is_active': True,
                'created_by': user
            }
        )

        # إنشاء منتجات تجريبية
        products_data = [
            ('منتج تجريبي A', 'PROD-A', 'منتج تجريبي للاختبار A'),
            ('منتج تجريبي B', 'PROD-B', 'منتج تجريبي للاختبار B'),
            ('مادة خام 1', 'RAW-1', 'مادة خام تجريبية 1'),
            ('مادة خام 2', 'RAW-2', 'مادة خام تجريبية 2'),
        ]

        products = []
        for name, code, desc in products_data:
            product, created = ProductDefinition.objects.get_or_create(
                name=name,
                defaults={
                    'code': code,
                    'description': desc,
                    'unit_of_measure': 'قطعة',
                    'is_active': True,
                    'created_by': user
                }
            )
            products.append(product)

        # إنشاء خطوط إنتاج تجريبية
        production_lines_data = [
            ('خط الإنتاج الأول', 'LINE-01', 'خط الإنتاج الرئيسي', 100, 85),
            ('خط الإنتاج الثاني', 'LINE-02', 'خط الإنتاج الثانوي', 80, 90),
            ('خط التعبئة والتغليف', 'LINE-03', 'خط التعبئة والتغليف', 150, 88),
        ]

        production_lines = []
        for name, code, desc, capacity, efficiency in production_lines_data:
            line, created = ProductionLine.objects.get_or_create(
                name=name,
                defaults={
                    'code': code,
                    'description': desc,
                    'warehouse': warehouse1,
                    'supervisor': user,
                    'capacity_per_hour': Decimal(str(capacity)),
                    'efficiency_rate': Decimal(str(efficiency)),
                    'installation_date': date.today() - timedelta(days=random.randint(30, 365)),
                    'status': 'operational',
                    'is_active': True,
                    'created_by': user
                }
            )
            production_lines.append(line)

        # إنشاء فئات المعدات
        equipment_categories_data = [
            ('آلات القطع', 'CUT-MACH', 'آلات القطع والتشكيل'),
            ('آلات اللحام', 'WELD-MACH', 'آلات اللحام والتجميع'),
            ('آلات التعبئة', 'PACK-MACH', 'آلات التعبئة والتغليف'),
            ('أنظمة النقل', 'CONV-SYS', 'أنظمة النقل والحزام'),
        ]

        equipment_categories = []
        for name, code, desc in equipment_categories_data:
            category, created = EquipmentCategory.objects.get_or_create(
                name=name,
                defaults={
                    'code': code,
                    'description': desc,
                    'is_active': True,
                    'created_by': user
                }
            )
            equipment_categories.append(category)

        # إنشاء معدات تجريبية
        equipment_data = [
            ('آلة القطع الرئيسية', 'CUT-001', 0, 0, 'Siemens', 'CNC-2000', 'SN123456', 2020),
            ('آلة اللحام الأوتوماتيكية', 'WELD-001', 1, 1, 'KUKA', 'WELD-500', 'SN789012', 2019),
            ('خط التعبئة السريع', 'PACK-001', 2, 2, 'Bosch', 'PACK-300', 'SN345678', 2021),
            ('نظام النقل الرئيسي', 'CONV-001', 3, 0, 'Festo', 'CONV-100', 'SN901234', 2018),
            ('آلة القطع الثانوية', 'CUT-002', 0, 1, 'Fanuc', 'CNC-1500', 'SN567890', 2022),
        ]

        equipment_list = []
        for name, code, cat_idx, line_idx, manufacturer, model, serial, year in equipment_data:
            equipment, created = Equipment.objects.get_or_create(
                name=name,
                defaults={
                    'code': code,
                    'category': equipment_categories[cat_idx],
                    'production_line': production_lines[line_idx],
                    'manufacturer': manufacturer,
                    'model': model,
                    'serial_number': serial,
                    'year_manufactured': year,
                    'rated_capacity': Decimal(str(random.randint(50, 200))),
                    'capacity_unit': 'قطعة/ساعة',
                    'power_consumption': Decimal(str(random.randint(10, 50))),
                    'status': random.choice(['operational', 'maintenance', 'idle']),
                    'condition': random.choice(['excellent', 'good', 'fair']),
                    'installation_date': date.today() - timedelta(days=random.randint(100, 1000)),
                    'last_maintenance_date': date.today() - timedelta(days=random.randint(1, 90)),
                    'next_maintenance_date': date.today() + timedelta(days=random.randint(1, 30)),
                    'purchase_cost': Decimal(str(random.randint(50000, 500000))),
                    'current_value': Decimal(str(random.randint(30000, 400000))),
                    'location': f'قسم {random.randint(1, 5)}',
                    'is_active': True,
                    'created_by': user
                }
            )
            equipment_list.append(equipment)

        # إنشاء أنواع الصيانة
        maintenance_types_data = [
            ('صيانة دورية', 'ROUTINE', 'صيانة دورية منتظمة', True, 4),
            ('صيانة وقائية', 'PREVENT', 'صيانة وقائية لمنع الأعطال', True, 8),
            ('إصلاح عطل', 'REPAIR', 'إصلاح عطل طارئ', False, 2),
            ('استبدال قطع', 'REPLACE', 'استبدال قطع الغيار', False, 6),
        ]

        maintenance_types = []
        for name, code, desc, is_preventive, duration in maintenance_types_data:
            mtype, created = MaintenanceType.objects.get_or_create(
                name=name,
                defaults={
                    'code': code,
                    'description': desc,
                    'is_preventive': is_preventive,
                    'estimated_duration_hours': Decimal(str(duration)),
                    'is_active': True
                }
            )
            maintenance_types.append(mtype)

        # إنشاء جدولة صيانة
        for equipment in equipment_list[:3]:  # للمعدات الثلاث الأولى فقط
            for mtype in maintenance_types[:2]:  # للصيانة الدورية والوقائية فقط
                MaintenanceSchedule.objects.get_or_create(
                    equipment=equipment,
                    maintenance_type=mtype,
                    defaults={
                        'frequency': random.choice(['monthly', 'quarterly']),
                        'frequency_value': 1,
                        'last_maintenance_date': date.today() - timedelta(days=random.randint(1, 60)),
                        'next_maintenance_date': date.today() + timedelta(days=random.randint(1, 30)),
                        'is_active': True,
                        'created_by': user
                    }
                )

        # إنشاء سجلات صيانة
        for i in range(10):
            equipment = random.choice(equipment_list)
            mtype = random.choice(maintenance_types)
            scheduled_date = timezone.now() - timedelta(days=random.randint(1, 30))
            
            MaintenanceRecord.objects.get_or_create(
                equipment=equipment,
                maintenance_type=mtype,
                title=f'صيانة {equipment.name} - {mtype.name}',
                defaults={
                    'description': f'تنفيذ {mtype.name} للمعدة {equipment.name}',
                    'priority': random.choice(['normal', 'high', 'low']),
                    'status': random.choice(['completed', 'in_progress', 'scheduled']),
                    'scheduled_date': scheduled_date,
                    'actual_start_date': scheduled_date,
                    'actual_end_date': scheduled_date + timedelta(hours=random.randint(2, 8)),
                    'estimated_duration_hours': Decimal(str(random.randint(2, 8))),
                    'actual_duration_hours': Decimal(str(random.randint(2, 8))),
                    'assigned_technician': user,
                    'performed_by': user,
                    'labor_cost': Decimal(str(random.randint(500, 2000))),
                    'parts_cost': Decimal(str(random.randint(200, 1000))),
                    'external_cost': Decimal(str(random.randint(0, 500))),
                    'work_performed': 'تم تنفيذ الصيانة بنجاح',
                    'created_by': user
                }
            )

        # إنشاء مقاييس أداء
        for i in range(20):
            line = random.choice(production_lines)
            equipment = random.choice([eq for eq in equipment_list if eq.production_line == line])
            
            PerformanceMetric.objects.get_or_create(
                production_line=line,
                equipment=equipment,
                metric_type=random.choice(['oee', 'availability', 'performance', 'quality']),
                measurement_date=date.today() - timedelta(days=random.randint(1, 7)),
                defaults={
                    'shift': random.choice(['morning', 'evening', 'night']),
                    'target_value': Decimal(str(random.randint(80, 95))),
                    'actual_value': Decimal(str(random.randint(70, 100))),
                    'unit': '%',
                    'created_by': user
                }
            )

        # إنشاء سجلات توقف
        for i in range(15):
            line = random.choice(production_lines)
            equipment = random.choice([eq for eq in equipment_list if eq.production_line == line])
            start_time = timezone.now() - timedelta(days=random.randint(1, 7), hours=random.randint(1, 8))
            
            # بعض السجلات مستمرة (بدون وقت انتهاء)
            is_ongoing = random.choice([True, False, False, False])  # 25% احتمال أن يكون مستمراً
            end_time = None if is_ongoing else start_time + timedelta(hours=random.randint(1, 6))
            
            DowntimeRecord.objects.get_or_create(
                production_line=line,
                equipment=equipment,
                title=f'توقف {equipment.name}',
                defaults={
                    'downtime_type': random.choice(['breakdown', 'maintenance', 'material_shortage', 'quality_issue']),
                    'severity': random.choice(['low', 'medium', 'high']),
                    'start_time': start_time,
                    'end_time': end_time,
                    'description': f'توقف في المعدة {equipment.name} بسبب مشكلة فنية',
                    'root_cause': 'تحت التحقيق',
                    'reported_by': user,
                    'resolved_by': user if end_time else None,
                    'production_loss': Decimal(str(random.randint(10, 100))),
                    'cost_impact': Decimal(str(random.randint(1000, 10000))),
                    'is_resolved': end_time is not None
                }
            )

        # إنشاء قوائم مواد تجريبية
        for i, product in enumerate(products[:2]):  # للمنتجين الأولين فقط
            BillOfMaterials.objects.get_or_create(
                name=f'قائمة مواد {product.name}',
                defaults={
                    'code': f'BOM-{product.code}',
                    'product': product,
                    'version': '1.0',
                    'quantity_produced': Decimal('1.0'),
                    'production_time_minutes': random.randint(30, 120),
                    'labor_cost': Decimal(str(random.randint(100, 500))),
                    'overhead_cost': Decimal(str(random.randint(50, 200))),
                    'effective_date': date.today(),
                    'status': 'active',
                    'created_by': user
                }
            )

        self.stdout.write(
            self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!')
        )
        self.stdout.write('البيانات المنشأة:')
        self.stdout.write(f'- {len(production_lines)} خطوط إنتاج')
        self.stdout.write(f'- {len(equipment_categories)} فئات معدات')
        self.stdout.write(f'- {len(equipment_list)} معدات')
        self.stdout.write(f'- {len(maintenance_types)} أنواع صيانة')
        self.stdout.write(f'- {MaintenanceRecord.objects.count()} سجل صيانة')
        self.stdout.write(f'- {PerformanceMetric.objects.count()} مقياس أداء')
        self.stdout.write(f'- {DowntimeRecord.objects.count()} سجل توقف')
        self.stdout.write(f'- {BillOfMaterials.objects.count()} قائمة مواد')
