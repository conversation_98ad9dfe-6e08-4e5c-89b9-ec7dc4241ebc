from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg
from django.http import JsonResponse
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal

from .models import (
    ProductionLine, WorkCenter, BillOfMaterials, BOMItem, ProductionOrder
)
from .forms import (
    ProductionLineForm, WorkCenterForm, BillOfMaterialsForm,
    BOMItemForm, ProductionOrderForm
)
from definitions.models import WarehouseDefinition, ProductDefinition


@login_required
def dashboard(request):
    """لوحة التحكم الرئيسية لإدارة الإنتاج"""

    # إحصائيات سريعة
    total_production_lines = ProductionLine.objects.filter(is_active=True).count()
    active_production_lines = ProductionLine.objects.filter(is_active=True, status='operational').count()
    total_boms = BillOfMaterials.objects.filter(is_active=True).count()
    active_boms = BillOfMaterials.objects.filter(is_active=True, status='active').count()

    # أوامر الإنتاج
    total_orders = ProductionOrder.objects.filter(is_active=True).count()
    active_orders = ProductionOrder.objects.filter(
        is_active=True,
        status__in=['planned', 'released', 'in_progress']
    ).count()
    completed_orders_today = ProductionOrder.objects.filter(
        is_active=True,
        status='completed',
        actual_end_date__date=date.today()
    ).count()
    overdue_orders = ProductionOrder.objects.filter(
        is_active=True,
        status__in=['planned', 'released', 'in_progress'],
        planned_end_date__lt=timezone.now()
    ).count()

    # أحدث أوامر الإنتاج
    recent_orders = ProductionOrder.objects.filter(is_active=True).select_related(
        'bom__product', 'production_line'
    ).order_by('-created_at')[:10]

    # خطوط الإنتاج النشطة
    active_lines = ProductionLine.objects.filter(
        is_active=True, status='operational'
    ).select_related('warehouse')[:8]

    # قوائم المواد النشطة
    active_bom_list = BillOfMaterials.objects.filter(
        is_active=True, status='active'
    ).select_related('product').order_by('-effective_date')[:8]

    context = {
        'page_title': 'إدارة الإنتاج',
        'total_production_lines': total_production_lines,
        'active_production_lines': active_production_lines,
        'total_boms': total_boms,
        'active_boms': active_boms,
        'total_orders': total_orders,
        'active_orders': active_orders,
        'completed_orders_today': completed_orders_today,
        'overdue_orders': overdue_orders,
        'recent_orders': recent_orders,
        'active_lines': active_lines,
        'active_bom_list': active_bom_list,
    }
    return render(request, 'production/dashboard.html', context)


@login_required
def production_line_list(request):
    """قائمة خطوط الإنتاج"""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    warehouse_filter = request.GET.get('warehouse', '')

    lines = ProductionLine.objects.select_related('warehouse', 'supervisor').filter(is_active=True)

    if search_query:
        lines = lines.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if status_filter:
        lines = lines.filter(status=status_filter)

    if warehouse_filter:
        lines = lines.filter(warehouse_id=warehouse_filter)

    lines = lines.order_by('name')

    # Pagination
    paginator = Paginator(lines, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # للفلترة
    warehouses = WarehouseDefinition.objects.filter(is_active=True)

    context = {
        'page_title': 'خطوط الإنتاج',
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'warehouse_filter': warehouse_filter,
        'warehouses': warehouses,
        'status_choices': ProductionLine.STATUS_CHOICES,
    }
    return render(request, 'production/production_line_list.html', context)


@login_required
def production_line_create(request):
    """إنشاء خط إنتاج جديد"""
    if request.method == 'POST':
        form = ProductionLineForm(request.POST)
        if form.is_valid():
            line = form.save(commit=False)
            line.created_by = request.user
            line.save()
            messages.success(request, f'تم إنشاء خط الإنتاج "{line.name}" بنجاح')
            return redirect('production:production_line_detail', pk=line.pk)
    else:
        form = ProductionLineForm()

    context = {
        'page_title': 'إنشاء خط إنتاج جديد',
        'form': form,
    }
    return render(request, 'production/production_line_form.html', context)


@login_required
def production_line_detail(request, pk):
    """تفاصيل خط الإنتاج"""
    line = get_object_or_404(ProductionLine, pk=pk, is_active=True)

    # إحصائيات خط الإنتاج
    work_centers_count = line.work_centers.filter(is_active=True).count()
    active_orders = line.production_orders.filter(
        is_active=True,
        status__in=['planned', 'released', 'in_progress']
    ).count()
    completed_orders_month = line.production_orders.filter(
        is_active=True,
        status='completed',
        actual_end_date__month=date.today().month,
        actual_end_date__year=date.today().year
    ).count()

    # أحدث أوامر الإنتاج
    recent_orders = line.production_orders.filter(is_active=True).select_related(
        'bom__product'
    ).order_by('-created_at')[:5]

    # مراكز العمل
    work_centers = line.work_centers.filter(is_active=True).order_by('name')[:6]

    context = {
        'page_title': f'تفاصيل خط الإنتاج - {line.name}',
        'line': line,
        'work_centers_count': work_centers_count,
        'active_orders': active_orders,
        'completed_orders_month': completed_orders_month,
        'recent_orders': recent_orders,
        'work_centers': work_centers,
    }
    return render(request, 'production/production_line_detail.html', context)


@login_required
def production_line_update(request, pk):
    """تحديث خط الإنتاج"""
    line = get_object_or_404(ProductionLine, pk=pk, is_active=True)

    if request.method == 'POST':
        form = ProductionLineForm(request.POST, instance=line)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث خط الإنتاج "{line.name}" بنجاح')
            return redirect('production:production_line_detail', pk=line.pk)
    else:
        form = ProductionLineForm(instance=line)

    context = {
        'page_title': f'تحديث خط الإنتاج - {line.name}',
        'form': form,
        'line': line,
    }
    return render(request, 'production/production_line_form.html', context)


@login_required
def production_line_delete(request, pk):
    """حذف خط الإنتاج"""
    line = get_object_or_404(ProductionLine, pk=pk, is_active=True)

    if request.method == 'POST':
        line.is_active = False
        line.save()
        messages.success(request, f'تم حذف خط الإنتاج "{line.name}" بنجاح')
        return redirect('production:production_line_list')

    context = {
        'page_title': f'حذف خط الإنتاج - {line.name}',
        'line': line,
    }
    return render(request, 'production/production_line_confirm_delete.html', context)


@login_required
def bom_list(request):
    """قائمة قوائم المواد"""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    product_filter = request.GET.get('product', '')

    boms = BillOfMaterials.objects.select_related('product').filter(is_active=True)

    if search_query:
        boms = boms.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(product__name__icontains=search_query)
        )

    if status_filter:
        boms = boms.filter(status=status_filter)

    if product_filter:
        boms = boms.filter(product_id=product_filter)

    boms = boms.order_by('-effective_date', 'product__name')

    # Pagination
    paginator = Paginator(boms, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # للفلترة
    products = ProductDefinition.objects.filter(is_active=True)

    context = {
        'page_title': 'قوائم المواد (BOM)',
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'product_filter': product_filter,
        'products': products,
        'status_choices': BillOfMaterials.STATUS_CHOICES,
    }
    return render(request, 'production/bom_list.html', context)


@login_required
def bom_create(request):
    """إنشاء قائمة مواد جديدة"""
    if request.method == 'POST':
        form = BillOfMaterialsForm(request.POST)
        if form.is_valid():
            bom = form.save(commit=False)
            bom.created_by = request.user
            bom.save()
            messages.success(request, f'تم إنشاء قائمة المواد "{bom.name}" بنجاح')
            return redirect('production:bom_detail', pk=bom.pk)
    else:
        form = BillOfMaterialsForm()

    context = {
        'page_title': 'إنشاء قائمة مواد جديدة',
        'form': form,
    }
    return render(request, 'production/bom_form.html', context)


@login_required
def bom_detail(request, pk):
    """تفاصيل قائمة المواد"""
    bom = get_object_or_404(BillOfMaterials, pk=pk, is_active=True)

    # عناصر قائمة المواد
    items = bom.items.select_related('material', 'supplier').filter(is_active=True).order_by('sequence')

    # إحصائيات
    total_items = items.count()
    critical_items = items.filter(is_critical=True).count()
    total_material_cost = sum(item.total_cost for item in items)

    context = {
        'page_title': f'تفاصيل قائمة المواد - {bom.name}',
        'bom': bom,
        'items': items,
        'total_items': total_items,
        'critical_items': critical_items,
        'total_material_cost': total_material_cost,
    }
    return render(request, 'production/bom_detail.html', context)


@login_required
def production_order_list(request):
    """قائمة أوامر الإنتاج"""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    priority_filter = request.GET.get('priority', '')
    line_filter = request.GET.get('production_line', '')

    orders = ProductionOrder.objects.select_related(
        'bom__product', 'production_line'
    ).filter(is_active=True)

    if search_query:
        orders = orders.filter(
            Q(order_number__icontains=search_query) |
            Q(bom__name__icontains=search_query) |
            Q(bom__product__name__icontains=search_query)
        )

    if status_filter:
        orders = orders.filter(status=status_filter)

    if priority_filter:
        orders = orders.filter(priority=priority_filter)

    if line_filter:
        orders = orders.filter(production_line_id=line_filter)

    orders = orders.order_by('-created_at')

    # Pagination
    paginator = Paginator(orders, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # للفلترة
    production_lines = ProductionLine.objects.filter(is_active=True)

    context = {
        'page_title': 'أوامر الإنتاج',
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'line_filter': line_filter,
        'production_lines': production_lines,
        'status_choices': ProductionOrder.STATUS_CHOICES,
        'priority_choices': ProductionOrder.PRIORITY_CHOICES,
    }
    return render(request, 'production/production_order_list.html', context)


# Views مؤقتة للصفحات المتبقية
@login_required
def work_center_list(request):
    """قائمة مراكز العمل"""
    centers = WorkCenter.objects.select_related('production_line').filter(is_active=True)

    context = {
        'page_title': 'مراكز العمل',
        'centers': centers,
    }
    return render(request, 'production/work_center_list.html', context)


@login_required
def work_center_create(request):
    """إنشاء مركز عمل جديد"""
    if request.method == 'POST':
        form = WorkCenterForm(request.POST)
        if form.is_valid():
            center = form.save(commit=False)
            center.created_by = request.user
            center.save()
            messages.success(request, f'تم إنشاء مركز العمل "{center.name}" بنجاح')
            return redirect('production:work_center_list')
    else:
        form = WorkCenterForm()

    context = {
        'page_title': 'إنشاء مركز عمل جديد',
        'form': form,
    }
    return render(request, 'production/work_center_form.html', context)


@login_required
def work_center_detail(request, pk):
    """تفاصيل مركز العمل"""
    center = get_object_or_404(WorkCenter, pk=pk, is_active=True)

    context = {
        'page_title': f'تفاصيل مركز العمل - {center.name}',
        'center': center,
    }
    return render(request, 'production/work_center_detail.html', context)


@login_required
def work_center_update(request, pk):
    """تحديث مركز العمل"""
    center = get_object_or_404(WorkCenter, pk=pk, is_active=True)

    if request.method == 'POST':
        form = WorkCenterForm(request.POST, instance=center)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث مركز العمل "{center.name}" بنجاح')
            return redirect('production:work_center_detail', pk=center.pk)
    else:
        form = WorkCenterForm(instance=center)

    context = {
        'page_title': f'تحديث مركز العمل - {center.name}',
        'form': form,
        'center': center,
    }
    return render(request, 'production/work_center_form.html', context)


@login_required
def work_center_delete(request, pk):
    """حذف مركز العمل"""
    center = get_object_or_404(WorkCenter, pk=pk, is_active=True)

    if request.method == 'POST':
        center.is_active = False
        center.save()
        messages.success(request, f'تم حذف مركز العمل "{center.name}" بنجاح')
        return redirect('production:work_center_list')

    return redirect('production:work_center_list')


# Views مؤقتة أخرى
@login_required
def bom_update(request, pk):
    """تحديث قائمة المواد"""
    bom = get_object_or_404(BillOfMaterials, pk=pk, is_active=True)

    if request.method == 'POST':
        form = BillOfMaterialsForm(request.POST, instance=bom)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث قائمة المواد "{bom.name}" بنجاح')
            return redirect('production:bom_detail', pk=bom.pk)
    else:
        form = BillOfMaterialsForm(instance=bom)

    context = {
        'page_title': f'تحديث قائمة المواد - {bom.name}',
        'form': form,
        'bom': bom,
    }
    return render(request, 'production/bom_form.html', context)


@login_required
def bom_delete(request, pk):
    """حذف قائمة المواد"""
    bom = get_object_or_404(BillOfMaterials, pk=pk, is_active=True)

    if request.method == 'POST':
        bom.is_active = False
        bom.save()
        messages.success(request, f'تم حذف قائمة المواد "{bom.name}" بنجاح')
        return redirect('production:bom_list')

    return redirect('production:bom_list')


@login_required
def bom_copy(request, pk):
    """نسخ قائمة المواد"""
    original_bom = get_object_or_404(BillOfMaterials, pk=pk, is_active=True)

    # إنشاء نسخة جديدة
    new_bom = BillOfMaterials.objects.create(
        name=f"{original_bom.name} - نسخة",
        code=f"{original_bom.code}-COPY",
        product=original_bom.product,
        version="1.0",
        quantity_produced=original_bom.quantity_produced,
        production_time_minutes=original_bom.production_time_minutes,
        labor_cost=original_bom.labor_cost,
        overhead_cost=original_bom.overhead_cost,
        status='draft',
        created_by=request.user
    )

    # نسخ العناصر
    for item in original_bom.items.filter(is_active=True):
        BOMItem.objects.create(
            bom=new_bom,
            material=item.material,
            sequence=item.sequence,
            item_type=item.item_type,
            quantity_required=item.quantity_required,
            unit_cost=item.unit_cost,
            waste_percentage=item.waste_percentage,
            is_critical=item.is_critical,
            lead_time_days=item.lead_time_days,
            supplier=item.supplier,
            notes=item.notes,
            created_by=request.user
        )

    messages.success(request, f'تم نسخ قائمة المواد "{original_bom.name}" بنجاح')
    return redirect('production:bom_detail', pk=new_bom.pk)


# Views مؤقتة للباقي
@login_required
def bom_item_list(request):
    """قائمة عناصر قوائم المواد"""
    return render(request, 'production/bom_item_list.html', {'page_title': 'عناصر قوائم المواد'})

@login_required
def bom_item_create(request):
    """إنشاء عنصر قائمة مواد جديد"""
    return render(request, 'production/bom_item_form.html', {'page_title': 'إنشاء عنصر قائمة مواد'})

@login_required
def bom_item_update(request, pk):
    """تحديث عنصر قائمة المواد"""
    return render(request, 'production/bom_item_form.html', {'page_title': 'تحديث عنصر قائمة المواد'})

@login_required
def bom_item_delete(request, pk):
    """حذف عنصر قائمة المواد"""
    return redirect('production:bom_item_list')

@login_required
def production_order_create(request):
    """إنشاء أمر إنتاج جديد"""
    if request.method == 'POST':
        form = ProductionOrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            order.save()
            messages.success(request, f'تم إنشاء أمر الإنتاج "{order.order_number}" بنجاح')
            return redirect('production:production_order_detail', pk=order.pk)
    else:
        form = ProductionOrderForm()

    context = {
        'page_title': 'إنشاء أمر إنتاج جديد',
        'form': form,
    }
    return render(request, 'production/production_order_form.html', context)

@login_required
def production_order_detail(request, pk):
    """تفاصيل أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk, is_active=True)

    context = {
        'page_title': f'تفاصيل أمر الإنتاج - {order.order_number}',
        'order': order,
    }
    return render(request, 'production/production_order_detail.html', context)

@login_required
def production_order_update(request, pk):
    """تحديث أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk, is_active=True)

    if request.method == 'POST':
        form = ProductionOrderForm(request.POST, instance=order)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث أمر الإنتاج "{order.order_number}" بنجاح')
            return redirect('production:production_order_detail', pk=order.pk)
    else:
        form = ProductionOrderForm(instance=order)

    context = {
        'page_title': f'تحديث أمر الإنتاج - {order.order_number}',
        'form': form,
        'order': order,
    }
    return render(request, 'production/production_order_form.html', context)

@login_required
def production_order_delete(request, pk):
    """حذف أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk, is_active=True)

    if request.method == 'POST':
        order.is_active = False
        order.save()
        messages.success(request, f'تم حذف أمر الإنتاج "{order.order_number}" بنجاح')
        return redirect('production:production_order_list')

    return redirect('production:production_order_list')

@login_required
def production_order_start(request, pk):
    """بدء أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk, is_active=True)

    if order.status in ['planned', 'released']:
        order.status = 'in_progress'
        order.actual_start_date = timezone.now()
        order.save()
        messages.success(request, f'تم بدء أمر الإنتاج "{order.order_number}" بنجاح')

    return redirect('production:production_order_detail', pk=order.pk)

@login_required
def production_order_complete(request, pk):
    """إكمال أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk, is_active=True)

    if order.status == 'in_progress':
        order.status = 'completed'
        order.actual_end_date = timezone.now()
        order.quantity_produced = order.quantity_ordered  # يمكن تعديل هذا لاحقاً
        order.save()
        messages.success(request, f'تم إكمال أمر الإنتاج "{order.order_number}" بنجاح')

    return redirect('production:production_order_detail', pk=order.pk)

@login_required
def production_order_cancel(request, pk):
    """إلغاء أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk, is_active=True)

    if order.status not in ['completed', 'cancelled']:
        order.status = 'cancelled'
        order.save()
        messages.success(request, f'تم إلغاء أمر الإنتاج "{order.order_number}" بنجاح')

    return redirect('production:production_order_detail', pk=order.pk)

@login_required
def reports_dashboard(request):
    """لوحة تحكم التقارير"""
    return render(request, 'production/reports_dashboard.html', {'page_title': 'تقارير الإنتاج'})

@login_required
def production_report(request):
    """تقرير الإنتاج"""
    return render(request, 'production/production_report.html', {'page_title': 'تقرير الإنتاج'})

@login_required
def efficiency_report(request):
    """تقرير الكفاءة"""
    return render(request, 'production/efficiency_report.html', {'page_title': 'تقرير الكفاءة'})

@login_required
def cost_report(request):
    """تقرير التكاليف"""
    return render(request, 'production/cost_report.html', {'page_title': 'تقرير التكاليف'})

# API Views
@login_required
def api_production_lines(request):
    """API لخطوط الإنتاج"""
    lines = ProductionLine.objects.filter(is_active=True).values('id', 'name', 'code')
    return JsonResponse(list(lines), safe=False)

@login_required
def api_bom_by_product(request):
    """API لقوائم المواد حسب المنتج"""
    product_id = request.GET.get('product_id')
    if product_id:
        boms = BillOfMaterials.objects.filter(
            product_id=product_id,
            is_active=True,
            status='active'
        ).values('id', 'name', 'version')
        return JsonResponse(list(boms), safe=False)
    return JsonResponse([], safe=False)

@login_required
def api_dashboard_stats(request):
    """API لإحصائيات لوحة التحكم"""
    stats = {
        'total_lines': ProductionLine.objects.filter(is_active=True).count(),
        'active_orders': ProductionOrder.objects.filter(
            is_active=True,
            status__in=['planned', 'released', 'in_progress']
        ).count(),
        'completed_today': ProductionOrder.objects.filter(
            is_active=True,
            status='completed',
            actual_end_date__date=date.today()
        ).count(),
    }
    return JsonResponse(stats)
