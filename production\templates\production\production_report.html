{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .chart-container {
        height: 400px;
        background: white;
        border-radius: 15px;
        padding: 1rem;
        margin: 1rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-bar-chart me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'production:reports_dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="content-card">
        <h3>فلاتر التقرير</h3>
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="start_date" class="form-control" value="{{ request.GET.start_date }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="end_date" class="form-control" value="{{ request.GET.end_date }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">خط الإنتاج</label>
                <select name="production_line" class="form-select">
                    <option value="">جميع خطوط الإنتاج</option>
                    <!-- سيتم إضافة خطوط الإنتاج هنا -->
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search me-2"></i>تطبيق الفلاتر
                </button>
            </div>
        </form>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-md-3">
            <div class="content-card text-center">
                <h4 class="text-primary">إجمالي الأوامر</h4>
                <h2 class="text-dark">0</h2>
                <small class="text-muted">في الفترة المحددة</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="content-card text-center">
                <h4 class="text-success">أوامر مكتملة</h4>
                <h2 class="text-dark">0</h2>
                <small class="text-muted">نسبة الإنجاز: 0%</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="content-card text-center">
                <h4 class="text-warning">أوامر قيد التنفيذ</h4>
                <h2 class="text-dark">0</h2>
                <small class="text-muted">في المرحلة النشطة</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="content-card text-center">
                <h4 class="text-danger">أوامر متأخرة</h4>
                <h2 class="text-dark">0</h2>
                <small class="text-muted">تحتاج متابعة</small>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <div class="col-md-6">
            <div class="content-card">
                <h3>الإنتاج اليومي</h3>
                <div class="chart-container">
                    <div class="text-center">
                        <i class="bi bi-bar-chart" style="font-size: 3rem;"></i>
                        <p class="mt-2">سيتم عرض الرسم البياني هنا</p>
                        <small class="text-muted">يتطلب تطبيق مكتبة الرسوم البيانية</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="content-card">
                <h3>توزيع حالات الأوامر</h3>
                <div class="chart-container">
                    <div class="text-center">
                        <i class="bi bi-pie-chart" style="font-size: 3rem;"></i>
                        <p class="mt-2">سيتم عرض الرسم البياني هنا</p>
                        <small class="text-muted">يتطلب تطبيق مكتبة الرسوم البيانية</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول تفصيلي -->
    <div class="content-card">
        <h3>تفاصيل أوامر الإنتاج</h3>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم الأمر</th>
                        <th>المنتج</th>
                        <th>خط الإنتاج</th>
                        <th>الكمية المطلوبة</th>
                        <th>الكمية المنتجة</th>
                        <th>نسبة الإنجاز</th>
                        <th>الحالة</th>
                        <th>تاريخ البدء</th>
                        <th>تاريخ الانتهاء</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                            <p class="mt-2">لا توجد بيانات للعرض</p>
                            <small>قم بإنشاء أوامر إنتاج لعرض التقرير</small>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="content-card">
        <div class="row">
            <div class="col-md-6">
                <h4>تصدير التقرير</h4>
                <p class="text-muted">احفظ التقرير بصيغ مختلفة</p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-outline-success me-2" disabled>
                    <i class="bi bi-file-earmark-excel me-2"></i>Excel
                </button>
                <button class="btn btn-outline-danger me-2" disabled>
                    <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                </button>
                <button class="btn btn-outline-primary" disabled>
                    <i class="bi bi-printer me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
