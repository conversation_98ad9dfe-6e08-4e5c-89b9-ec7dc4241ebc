{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-tools me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'production:work_center_create' %}" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>إنشاء جديد
                </a>
            </div>
        </div>
    </div>

    <div class="content-card">
        {% if centers %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>الكود</th>
                        <th>خط الإنتاج</th>
                        <th>الطاقة الإنتاجية</th>
                        <th>تكلفة العمالة/ساعة</th>
                        <th>التكاليف الإضافية/ساعة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for center in centers %}
                    <tr>
                        <td>
                            <a href="{% url 'production:work_center_detail' center.pk %}">
                                {{ center.name }}
                            </a>
                        </td>
                        <td>{{ center.code }}</td>
                        <td>{{ center.production_line.name }}</td>
                        <td>{{ center.capacity_per_hour }} وحدة/ساعة</td>
                        <td>{{ center.labor_cost_per_hour }} جنيه</td>
                        <td>{{ center.overhead_cost_per_hour }} جنيه</td>
                        <td>
                            {% if center.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'production:work_center_detail' center.pk %}" 
                                   class="btn btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'production:work_center_update' center.pk %}" 
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'production:work_center_delete' center.pk %}" 
                                   class="btn btn-outline-danger"
                                   onclick="return confirm('هل أنت متأكد من الحذف؟')">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-tools" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3">لا توجد مراكز عمل</h3>
            <p class="text-muted">لم يتم إنشاء أي مراكز عمل بعد</p>
            <a href="{% url 'production:work_center_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>إنشاء مركز عمل جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
