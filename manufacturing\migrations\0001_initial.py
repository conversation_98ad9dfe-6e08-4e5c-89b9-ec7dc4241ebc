# Generated by Django 5.2.4 on 2025-07-19 04:02

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0014_auto_20250714_0411'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BillOfMaterials',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم قائمة المواد')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود قائمة المواد')),
                ('version', models.Char<PERSON>ield(default='1.0', max_length=20, verbose_name='الإصدار')),
                ('quantity_produced', models.DecimalField(decimal_places=3, default=1, max_digits=10, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المنتجة')),
                ('production_time_minutes', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='وقت الإنتاج (دقيقة)')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='تكلفة العمالة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='التكاليف الإضافية')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('active', 'نشط'), ('inactive', 'غير نشط'), ('archived', 'مؤرشف')], default='draft', max_length=20, verbose_name='الحالة')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_boms', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_as_product', to='definitions.productdefinition', verbose_name='المنتج النهائي')),
            ],
            options={
                'verbose_name': 'قائمة مواد',
                'verbose_name_plural': 'قوائم المواد',
                'ordering': ['-created_at'],
                'unique_together': {('product', 'version')},
            },
        ),
        migrations.CreateModel(
            name='BOMItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_type', models.CharField(choices=[('raw_material', 'مادة خام'), ('component', 'مكون'), ('sub_assembly', 'تجميع فرعي')], default='raw_material', max_length=20, verbose_name='نوع العنصر')),
                ('quantity_required', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المطلوبة')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='تكلفة الوحدة')),
                ('waste_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة الفاقد %')),
                ('is_critical', models.BooleanField(default=False, verbose_name='مادة حرجة')),
                ('supplier_lead_time', models.IntegerField(default=0, verbose_name='مدة التوريد (أيام)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('sequence', models.IntegerField(default=1, verbose_name='التسلسل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_items', to='manufacturing.billofmaterials', verbose_name='قائمة المواد')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_as_material', to='definitions.productdefinition', verbose_name='المادة')),
            ],
            options={
                'verbose_name': 'عنصر قائمة مواد',
                'verbose_name_plural': 'عناصر قوائم المواد',
                'ordering': ['sequence', 'material__name'],
                'unique_together': {('bom', 'material')},
            },
        ),
        migrations.CreateModel(
            name='ProductionLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم خط الإنتاج')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود خط الإنتاج')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('capacity_per_hour', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الطاقة الإنتاجية/ساعة')),
                ('efficiency_rate', models.DecimalField(decimal_places=2, default=100.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='معدل الكفاءة %')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('maintenance', 'صيانة'), ('inactive', 'غير نشط'), ('breakdown', 'عطل')], default='active', max_length=20, verbose_name='الحالة')),
                ('installation_date', models.DateField(verbose_name='تاريخ التركيب')),
                ('last_maintenance', models.DateField(blank=True, null=True, verbose_name='آخر صيانة')),
                ('next_maintenance', models.DateField(blank=True, null=True, verbose_name='الصيانة القادمة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_production_lines', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_lines', to=settings.AUTH_USER_MODEL, verbose_name='المشرف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'خط إنتاج',
                'verbose_name_plural': 'خطوط الإنتاج',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductionOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الأمر')),
                ('quantity_ordered', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المطلوبة')),
                ('quantity_produced', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المنتجة')),
                ('quantity_scrapped', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية التالفة')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('planned', 'مخطط'), ('released', 'محرر'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('on_hold', 'معلق')], default='draft', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('normal', 'عادي'), ('high', 'عالي'), ('urgent', 'عاجل')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('planned_start_date', models.DateTimeField(verbose_name='تاريخ البدء المخطط')),
                ('planned_end_date', models.DateTimeField(verbose_name='تاريخ الانتهاء المخطط')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البدء الفعلي')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء الفعلي')),
                ('customer_order_ref', models.CharField(blank=True, max_length=100, null=True, verbose_name='مرجع طلب العميل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.billofmaterials', verbose_name='قائمة المواد')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_production_orders', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('production_line', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.productionline', verbose_name='خط الإنتاج')),
            ],
            options={
                'verbose_name': 'أمر إنتاج',
                'verbose_name_plural': 'أوامر الإنتاج',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductionOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_required', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المطلوبة')),
                ('quantity_consumed', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المستهلكة')),
                ('quantity_returned', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المرتجعة')),
                ('status', models.CharField(choices=[('planned', 'مخطط'), ('reserved', 'محجوز'), ('consumed', 'مستهلك'), ('returned', 'مرتجع')], default='planned', max_length=20, verbose_name='حالة الاستهلاك')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('bom_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.bomitem', verbose_name='عنصر قائمة المواد')),
                ('production_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='manufacturing.productionorder', verbose_name='أمر الإنتاج')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'عنصر أمر إنتاج',
                'verbose_name_plural': 'عناصر أوامر الإنتاج',
                'ordering': ['bom_item__sequence'],
            },
        ),
        migrations.CreateModel(
            name='QualityCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('check_type', models.CharField(choices=[('incoming', 'فحص المواد الواردة'), ('in_process', 'فحص أثناء الإنتاج'), ('final', 'فحص نهائي'), ('random', 'فحص عشوائي')], max_length=20, verbose_name='نوع الفحص')),
                ('check_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الفحص')),
                ('quantity_checked', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المفحوصة')),
                ('quantity_passed', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية الناجحة')),
                ('quantity_failed', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية الفاشلة')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('in_progress', 'قيد الفحص'), ('passed', 'نجح'), ('failed', 'فشل'), ('conditional', 'مشروط')], default='pending', max_length=20, verbose_name='حالة الفحص')),
                ('defect_description', models.TextField(blank=True, null=True, verbose_name='وصف العيوب')),
                ('corrective_action', models.TextField(blank=True, null=True, verbose_name='الإجراء التصحيحي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('inspector', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='quality_inspections', to=settings.AUTH_USER_MODEL, verbose_name='المفتش')),
                ('production_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='manufacturing.productionorder', verbose_name='أمر الإنتاج')),
            ],
            options={
                'verbose_name': 'فحص جودة',
                'verbose_name_plural': 'فحوصات الجودة',
                'ordering': ['-check_date'],
            },
        ),
        migrations.CreateModel(
            name='ProductionReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_date', models.DateField(verbose_name='تاريخ التقرير')),
                ('shift', models.CharField(choices=[('morning', 'الصباحية'), ('evening', 'المسائية'), ('night', 'الليلية')], max_length=10, verbose_name='الوردية')),
                ('quantity_produced', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المنتجة')),
                ('quantity_scrapped', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية التالفة')),
                ('downtime_minutes', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)], verbose_name='وقت التوقف (دقيقة)')),
                ('downtime_reason', models.TextField(blank=True, null=True, verbose_name='سبب التوقف')),
                ('efficiency_rate', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='معدل الكفاءة %')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_production_reports', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('operator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='production_reports', to=settings.AUTH_USER_MODEL, verbose_name='المشغل')),
                ('production_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_reports', to='manufacturing.productionorder', verbose_name='أمر الإنتاج')),
            ],
            options={
                'verbose_name': 'تقرير إنتاج',
                'verbose_name_plural': 'تقارير الإنتاج',
                'ordering': ['-report_date', '-shift'],
                'unique_together': {('production_order', 'report_date', 'shift')},
            },
        ),
    ]
