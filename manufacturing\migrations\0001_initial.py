# Generated by Django 5.2.4 on 2025-07-19 16:37

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0015_finishedproductmodel_productionstage_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ManufacturingOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم أمر التصنيع')),
                ('order_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الأمر')),
                ('expected_completion_date', models.DateTimeField(verbose_name='تاريخ الإنجاز المتوقع')),
                ('actual_completion_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإنجاز الفعلي')),
                ('quantity_to_produce', models.DecimalField(decimal_places=3, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.001'))], verbose_name='الكمية المطلوب إنتاجها')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('approved', 'معتمد'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('on_hold', 'معلق')], default='draft', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('estimated_raw_material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة المواد الخام المقدرة')),
                ('estimated_labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة العمالة المقدرة')),
                ('estimated_overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكاليف الإضافية المقدرة')),
                ('actual_raw_material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة المواد الخام الفعلية')),
                ('actual_labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة العمالة الفعلية')),
                ('actual_overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكاليف الإضافية الفعلية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('special_instructions', models.TextField(blank=True, verbose_name='تعليمات خاصة')),
                ('quality_requirements', models.TextField(blank=True, verbose_name='متطلبات الجودة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ بدء التنفيذ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_manufacturing_orders', to=settings.AUTH_USER_MODEL, verbose_name='معتمد بواسطة')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_manufacturing_orders', to=settings.AUTH_USER_MODEL, verbose_name='أكمل بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('final_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='manufacturing_orders', to='definitions.productdefinition', verbose_name='المنتج النهائي')),
                ('finished_goods_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='finished_goods_orders', to='definitions.warehousedefinition', verbose_name='مخزن المنتجات التامة')),
                ('raw_materials_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='raw_material_orders', to='definitions.warehousedefinition', verbose_name='مخزن المواد الخام')),
                ('started_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='started_manufacturing_orders', to=settings.AUTH_USER_MODEL, verbose_name='بدأ التنفيذ بواسطة')),
                ('unit_of_measure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.unitdefinition', verbose_name='وحدة القياس')),
            ],
            options={
                'verbose_name': 'أمر تصنيع',
                'verbose_name_plural': 'أوامر التصنيع',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ManufacturingInventoryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('raw_material_consumption', 'استهلاك مواد خام'), ('finished_goods_production', 'إنتاج منتجات تامة'), ('waste_disposal', 'التخلص من المخلفات'), ('return_to_stock', 'إرجاع للمخزون'), ('adjustment', 'تسوية')], max_length=30, verbose_name='نوع الحركة')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=12, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('transaction_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الحركة')),
                ('reference_number', models.CharField(blank=True, max_length=50, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_processed', models.BooleanField(default=False, verbose_name='تم المعالجة')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_manufacturing_transactions', to=settings.AUTH_USER_MODEL, verbose_name='معالج بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المنتج')),
                ('unit_of_measure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.unitdefinition', verbose_name='وحدة القياس')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
                ('manufacturing_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_transactions', to='manufacturing.manufacturingorder', verbose_name='أمر التصنيع')),
            ],
            options={
                'verbose_name': 'حركة مخزون التصنيع',
                'verbose_name_plural': 'حركات مخزون التصنيع',
                'ordering': ['-transaction_date'],
            },
        ),
        migrations.CreateModel(
            name='ManufacturingOrderRawMaterial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('required_quantity', models.DecimalField(decimal_places=3, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.001'))], verbose_name='الكمية المطلوبة')),
                ('allocated_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='الكمية المخصصة')),
                ('consumed_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='الكمية المستهلكة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_critical', models.BooleanField(default=False, verbose_name='مادة حرجة')),
                ('is_available', models.BooleanField(default=False, verbose_name='متوفرة')),
                ('shortage_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='كمية النقص')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('manufacturing_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='raw_materials', to='manufacturing.manufacturingorder', verbose_name='أمر التصنيع')),
                ('raw_material', models.ForeignKey(limit_choices_to={'product_type': 'raw_material'}, on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المادة الخام')),
                ('substitute_materials', models.ManyToManyField(blank=True, related_name='substitute_for', to='definitions.productdefinition', verbose_name='المواد البديلة')),
                ('unit_of_measure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.unitdefinition', verbose_name='وحدة القياس')),
            ],
            options={
                'verbose_name': 'مادة خام لأمر التصنيع',
                'verbose_name_plural': 'المواد الخام لأوامر التصنيع',
                'ordering': ['raw_material__name'],
            },
        ),
        migrations.CreateModel(
            name='ManufacturingOrderStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stage_name', models.CharField(max_length=100, verbose_name='اسم المرحلة')),
                ('stage_description', models.TextField(blank=True, verbose_name='وصف المرحلة')),
                ('sequence_number', models.IntegerField(verbose_name='رقم التسلسل')),
                ('planned_start_date', models.DateTimeField(verbose_name='تاريخ البدء المخطط')),
                ('planned_end_date', models.DateTimeField(verbose_name='تاريخ الانتهاء المخطط')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البدء الفعلي')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء الفعلي')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('on_hold', 'معلق'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('progress_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MinValueValidator(100)], verbose_name='نسبة الإنجاز')),
                ('estimated_labor_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='ساعات العمل المقدرة')),
                ('actual_labor_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='ساعات العمل الفعلية')),
                ('labor_cost_per_hour', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة العمالة/ساعة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='التكاليف الإضافية')),
                ('quality_check_required', models.BooleanField(default=False, verbose_name='يتطلب فحص جودة')),
                ('quality_check_passed', models.BooleanField(default=False, verbose_name='اجتاز فحص الجودة')),
                ('quality_notes', models.TextField(blank=True, verbose_name='ملاحظات الجودة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('equipment_used', models.TextField(blank=True, verbose_name='المعدات المستخدمة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_manufacturing_stages', to=settings.AUTH_USER_MODEL, verbose_name='مسند إلى')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_manufacturing_stages', to=settings.AUTH_USER_MODEL, verbose_name='أكمل بواسطة')),
                ('manufacturing_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_stages', to='manufacturing.manufacturingorder', verbose_name='أمر التصنيع')),
            ],
            options={
                'verbose_name': 'مرحلة أمر التصنيع',
                'verbose_name_plural': 'مراحل أوامر التصنيع',
                'ordering': ['sequence_number'],
            },
        ),
        migrations.CreateModel(
            name='ManufacturingQualityCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('check_type', models.CharField(choices=[('incoming_materials', 'فحص المواد الواردة'), ('in_process', 'فحص أثناء الإنتاج'), ('final_inspection', 'الفحص النهائي'), ('random_sampling', 'عينة عشوائية')], max_length=20, verbose_name='نوع الفحص')),
                ('check_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الفحص')),
                ('result', models.CharField(choices=[('passed', 'مقبول'), ('failed', 'مرفوض'), ('conditional', 'مقبول بشروط'), ('pending', 'قيد الفحص')], default='pending', max_length=15, verbose_name='النتيجة')),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MinValueValidator(100)], verbose_name='النقاط')),
                ('criteria_checked', models.TextField(verbose_name='المعايير المفحوصة')),
                ('defects_found', models.TextField(blank=True, verbose_name='العيوب المكتشفة')),
                ('corrective_actions', models.TextField(blank=True, verbose_name='الإجراءات التصحيحية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('test_results_file', models.FileField(blank=True, null=True, upload_to='manufacturing/quality_checks/', verbose_name='ملف نتائج الاختبار')),
                ('photos', models.TextField(blank=True, verbose_name='الصور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('inspector', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المفتش')),
                ('manufacturing_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='manufacturing.manufacturingorder', verbose_name='أمر التصنيع')),
                ('stage', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='manufacturing.manufacturingorderstage', verbose_name='المرحلة')),
            ],
            options={
                'verbose_name': 'فحص جودة التصنيع',
                'verbose_name_plural': 'فحوصات جودة التصنيع',
                'ordering': ['-check_date'],
            },
        ),
        migrations.AddIndex(
            model_name='manufacturingorder',
            index=models.Index(fields=['order_number'], name='manufacturi_order_n_1ff75d_idx'),
        ),
        migrations.AddIndex(
            model_name='manufacturingorder',
            index=models.Index(fields=['status'], name='manufacturi_status_d15bf6_idx'),
        ),
        migrations.AddIndex(
            model_name='manufacturingorder',
            index=models.Index(fields=['order_date'], name='manufacturi_order_d_d029d9_idx'),
        ),
        migrations.AddIndex(
            model_name='manufacturinginventorytransaction',
            index=models.Index(fields=['transaction_date'], name='manufacturi_transac_615428_idx'),
        ),
        migrations.AddIndex(
            model_name='manufacturinginventorytransaction',
            index=models.Index(fields=['transaction_type'], name='manufacturi_transac_3a5423_idx'),
        ),
        migrations.AddIndex(
            model_name='manufacturinginventorytransaction',
            index=models.Index(fields=['is_processed'], name='manufacturi_is_proc_692fa3_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='manufacturingorderrawmaterial',
            unique_together={('manufacturing_order', 'raw_material')},
        ),
        migrations.AlterUniqueTogether(
            name='manufacturingorderstage',
            unique_together={('manufacturing_order', 'sequence_number')},
        ),
    ]
