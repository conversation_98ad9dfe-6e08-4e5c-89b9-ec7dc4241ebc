{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin: 2rem 0;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #667eea;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .alert-custom {
        border: none;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        border-left: 4px solid #667eea;
    }

    .form-text-custom {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-diagram-3 me-3"></i>
                    {% if production_line %}تحديث خط الإنتاج{% else %}إنشاء خط إنتاج جديد{% endif %}
                </h1>
                <p class="text-white-50 mb-0 mt-2">
                    {% if production_line %}تحديث بيانات خط الإنتاج{% else %}إنشاء خط إنتاج جديد في النظام{% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:production_line_list' %}" class="btn btn-light">
                        <i class="bi bi-diagram-3 me-2"></i>خطوط الإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- النموذج -->
    <div class="form-container">
        <div class="alert-custom">
            <i class="bi bi-info-circle me-2"></i>
            <strong>ملاحظة:</strong> تأكد من إدخال جميع البيانات المطلوبة بدقة لضمان إدارة فعالة لخط الإنتاج.
        </div>

        <form method="post" id="productionLineForm">
            {% csrf_token %}
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">اسم خط الإنتاج</label>
                            {{ form.name }}
                            <div class="form-text-custom">اسم وصفي لخط الإنتاج</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">كود خط الإنتاج</label>
                            {{ form.code }}
                            <div class="form-text-custom">كود فريد لتمييز خط الإنتاج</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">وصف خط الإنتاج</label>
                            {{ form.description }}
                            <div class="form-text-custom">وصف تفصيلي لخط الإنتاج ووظائفه</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">المخزن المرتبط</label>
                            {{ form.warehouse }}
                            <div class="form-text-custom">المخزن المخصص لهذا خط الإنتاج</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المشرف</label>
                            {{ form.supervisor }}
                            <div class="form-text-custom">المشرف المسؤول عن خط الإنتاج</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المواصفات التقنية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-gear"></i>
                    المواصفات التقنية
                </h3>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">الطاقة الإنتاجية (ساعة)</label>
                            {{ form.capacity_per_hour }}
                            <div class="form-text-custom">الطاقة الإنتاجية المقدرة في الساعة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">معدل الكفاءة (%)</label>
                            {{ form.efficiency_rate }}
                            <div class="form-text-custom">معدل الكفاءة المتوقع لخط الإنتاج</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تاريخ التركيب</label>
                            {{ form.installation_date }}
                            <div class="form-text-custom">تاريخ تركيب وتشغيل خط الإنتاج</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">حالة التشغيل</label>
                            {{ form.status }}
                            <div class="form-text-custom">الحالة التشغيلية الحالية لخط الإنتاج</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">حالة النشاط</label>
                            <div class="form-check mt-2">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    خط الإنتاج نشط
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المعلومات المالية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-currency-dollar"></i>
                    المعلومات المالية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تكلفة الإنشاء</label>
                            {{ form.setup_cost }}
                            <div class="form-text-custom">التكلفة الإجمالية لإنشاء خط الإنتاج</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">التكلفة التشغيلية (ساعة)</label>
                            {{ form.operating_cost_per_hour }}
                            <div class="form-text-custom">التكلفة التشغيلية المقدرة في الساعة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-file-text"></i>
                    ملاحظات إضافية
                </h3>
                
                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    {{ form.notes }}
                    <div class="form-text-custom">أي ملاحظات أو معلومات إضافية عن خط الإنتاج</div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-3 justify-content-between align-items-center">
                <!-- أزرار العودة -->
                <div class="d-flex gap-2">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-2"></i>لوحة التحكم
                    </a>
                    <a href="{% url 'manufacturing:production_line_list' %}" class="btn btn-outline-primary">
                        <i class="bi bi-diagram-3 me-2"></i>خطوط الإنتاج
                    </a>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="d-flex gap-3">
                    <a href="{% url 'manufacturing:production_line_list' %}" class="btn-secondary-custom">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                    <button type="submit" class="btn-primary-custom">
                        <i class="bi bi-check-circle"></i>
                        {% if production_line %}تحديث خط الإنتاج{% else %}إنشاء خط الإنتاج{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('productionLineForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    // إنشاء كود تلقائي بناءً على اسم خط الإنتاج
    const nameInput = form.querySelector('input[name="name"]');
    const codeInput = form.querySelector('input[name="code"]');
    
    if (nameInput && codeInput && !codeInput.value) {
        nameInput.addEventListener('blur', function() {
            if (this.value && !codeInput.value) {
                const code = 'LINE-' + this.value.substring(0, 3).toUpperCase() + '-' + 
                           Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                codeInput.value = code;
            }
        });
    }

    // تعيين تاريخ اليوم كتاريخ تركيب افتراضي
    const installationDateInput = form.querySelector('input[name="installation_date"]');
    if (installationDateInput && !installationDateInput.value) {
        const today = new Date();
        installationDateInput.value = today.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
