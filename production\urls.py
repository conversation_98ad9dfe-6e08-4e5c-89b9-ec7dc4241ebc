from django.urls import path
from . import views

app_name = 'production'

urlpatterns = [
    # لوحة التحكم الرئيسية
    path('', views.dashboard, name='dashboard'),
    
    # خطوط الإنتاج
    path('lines/', views.production_line_list, name='production_line_list'),
    path('lines/create/', views.production_line_create, name='production_line_create'),
    path('lines/<uuid:pk>/', views.production_line_detail, name='production_line_detail'),
    path('lines/<uuid:pk>/edit/', views.production_line_update, name='production_line_update'),
    path('lines/<uuid:pk>/delete/', views.production_line_delete, name='production_line_delete'),
    
    # مراكز العمل
    path('work-centers/', views.work_center_list, name='work_center_list'),
    path('work-centers/create/', views.work_center_create, name='work_center_create'),
    path('work-centers/<uuid:pk>/', views.work_center_detail, name='work_center_detail'),
    path('work-centers/<uuid:pk>/edit/', views.work_center_update, name='work_center_update'),
    path('work-centers/<uuid:pk>/delete/', views.work_center_delete, name='work_center_delete'),
    
    # قوائم المواد (BOM)
    path('bom/', views.bom_list, name='bom_list'),
    path('bom/create/', views.bom_create, name='bom_create'),
    path('bom/<uuid:pk>/', views.bom_detail, name='bom_detail'),
    path('bom/<uuid:pk>/edit/', views.bom_update, name='bom_update'),
    path('bom/<uuid:pk>/delete/', views.bom_delete, name='bom_delete'),
    path('bom/<uuid:pk>/copy/', views.bom_copy, name='bom_copy'),
    
    # عناصر قوائم المواد
    path('bom-items/', views.bom_item_list, name='bom_item_list'),
    path('bom-items/create/', views.bom_item_create, name='bom_item_create'),
    path('bom-items/<uuid:pk>/edit/', views.bom_item_update, name='bom_item_update'),
    path('bom-items/<uuid:pk>/delete/', views.bom_item_delete, name='bom_item_delete'),
    
    # أوامر الإنتاج
    path('orders/', views.production_order_list, name='production_order_list'),
    path('orders/create/', views.production_order_create, name='production_order_create'),
    path('orders/<uuid:pk>/', views.production_order_detail, name='production_order_detail'),
    path('orders/<uuid:pk>/edit/', views.production_order_update, name='production_order_update'),
    path('orders/<uuid:pk>/delete/', views.production_order_delete, name='production_order_delete'),
    path('orders/<uuid:pk>/start/', views.production_order_start, name='production_order_start'),
    path('orders/<uuid:pk>/complete/', views.production_order_complete, name='production_order_complete'),
    path('orders/<uuid:pk>/cancel/', views.production_order_cancel, name='production_order_cancel'),
    
    # التقارير والإحصائيات
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/production/', views.production_report, name='production_report'),
    path('reports/efficiency/', views.efficiency_report, name='efficiency_report'),
    path('reports/costs/', views.cost_report, name='cost_report'),
    
    # API endpoints للبيانات الديناميكية
    path('api/production-lines/', views.api_production_lines, name='api_production_lines'),
    path('api/bom-by-product/', views.api_bom_by_product, name='api_bom_by_product'),
    path('api/dashboard-stats/', views.api_dashboard_stats, name='api_dashboard_stats'),
]
