{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        margin: 0 auto 1rem;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 900;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
        font-size: 1rem;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .card-title {
        font-size: 1.6rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.8rem;
    }

    .card-title i {
        color: #667eea;
        font-size: 1.8rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .info-item {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        padding: 1.5rem;
        border-left: 5px solid #667eea;
    }

    .info-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 700;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
    }

    .status-operational {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    }

    .status-maintenance {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
    }

    .status-idle {
        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        color: white;
        box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
    }

    .status-setup {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .btn-custom {
        padding: 0.8rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-outline-custom {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .btn-outline-custom:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .btn-danger-custom {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
    }

    .btn-danger-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        color: white;
    }

    .recent-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .recent-item {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
        padding: 1rem;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
    }

    .recent-item:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateX(5px);
    }

    .recent-item-title {
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .recent-item-meta {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #667eea;
    }

    @media (max-width: 1200px) {
        .content-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-custom {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-diagram-3 me-3"></i>
                    {{ line.name }}
                </h1>
                <p class="text-white-50 mb-0 mt-2">
                    <i class="bi bi-hash me-2"></i>{{ line.code }}
                    <span class="mx-3">|</span>
                    <i class="bi bi-building me-2"></i>{{ line.warehouse.name }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'production:production_line_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left me-2"></i>العودة
                    </a>
                    <span class="status-badge status-{{ line.status }}">
                        {{ line.get_status_display }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-tools"></i>
            </div>
            <div class="stat-number">{{ work_centers_count }}</div>
            <div class="stat-label">مراكز العمل</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-play-circle"></i>
            </div>
            <div class="stat-number">{{ active_orders }}</div>
            <div class="stat-label">أوامر نشطة</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-number">{{ completed_orders_month }}</div>
            <div class="stat-label">مكتمل هذا الشهر</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-speedometer2"></i>
            </div>
            <div class="stat-number">{{ line.efficiency_rate }}%</div>
            <div class="stat-label">معدل الكفاءة</div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="action-buttons">
        <a href="{% url 'production:production_line_update' line.pk %}" class="btn-custom btn-primary-custom">
            <i class="bi bi-pencil"></i>
            تعديل خط الإنتاج
        </a>
        <a href="{% url 'production:work_center_create' %}?production_line={{ line.pk }}" class="btn-custom btn-outline-custom">
            <i class="bi bi-plus-circle"></i>
            إضافة مركز عمل
        </a>
        <a href="{% url 'production:production_order_create' %}?production_line={{ line.pk }}" class="btn-custom btn-outline-custom">
            <i class="bi bi-clipboard-plus"></i>
            إنشاء أمر إنتاج
        </a>
        <a href="{% url 'production:production_line_delete' line.pk %}" class="btn-custom btn-danger-custom" 
           onclick="return confirm('هل أنت متأكد من حذف خط الإنتاج؟')">
            <i class="bi bi-trash"></i>
            حذف
        </a>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="content-grid">
        <!-- العمود الرئيسي -->
        <div>
            <!-- معلومات خط الإنتاج -->
            <div class="content-card">
                <h3 class="card-title">
                    <i class="bi bi-info-circle"></i>
                    معلومات خط الإنتاج
                </h3>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">الطاقة الإنتاجية</div>
                        <div class="info-value">{{ line.capacity_per_hour }} وحدة/ساعة</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">معدل الكفاءة</div>
                        <div class="info-value">{{ line.efficiency_rate }}%</div>
                    </div>
                    
                    {% if line.installation_date %}
                    <div class="info-item">
                        <div class="info-label">تاريخ التركيب</div>
                        <div class="info-value">{{ line.installation_date }}</div>
                    </div>
                    {% endif %}
                    
                    {% if line.supervisor %}
                    <div class="info-item">
                        <div class="info-label">المشرف</div>
                        <div class="info-value">{{ line.supervisor.get_full_name|default:line.supervisor.username }}</div>
                    </div>
                    {% endif %}
                    
                    {% if line.setup_cost %}
                    <div class="info-item">
                        <div class="info-label">تكلفة الإنشاء</div>
                        <div class="info-value">{{ line.setup_cost }} جنيه</div>
                    </div>
                    {% endif %}
                    
                    {% if line.operating_cost_per_hour %}
                    <div class="info-item">
                        <div class="info-label">التكلفة التشغيلية</div>
                        <div class="info-value">{{ line.operating_cost_per_hour }} جنيه/ساعة</div>
                    </div>
                    {% endif %}
                </div>
                
                {% if line.description %}
                <div class="mt-3">
                    <div class="info-label">الوصف</div>
                    <div class="info-value">{{ line.description }}</div>
                </div>
                {% endif %}
                
                {% if line.notes %}
                <div class="mt-3">
                    <div class="info-label">ملاحظات</div>
                    <div class="info-value">{{ line.notes }}</div>
                </div>
                {% endif %}
            </div>

            <!-- مراكز العمل -->
            <div class="content-card">
                <h3 class="card-title">
                    <i class="bi bi-tools"></i>
                    مراكز العمل
                </h3>
                
                <div class="recent-list">
                    {% for center in work_centers %}
                    <div class="recent-item">
                        <div class="recent-item-title">
                            <a href="{% url 'production:work_center_detail' center.pk %}" 
                               style="text-decoration: none; color: inherit;">
                                {{ center.name }}
                            </a>
                        </div>
                        <div class="recent-item-meta">
                            <span>{{ center.capacity_per_hour }} وحدة/ساعة</span>
                            <span>{{ center.code }}</span>
                        </div>
                    </div>
                    {% empty %}
                    <div class="empty-state">
                        <i class="bi bi-tools"></i>
                        <p>لا توجد مراكز عمل في هذا الخط</p>
                        <a href="{% url 'production:work_center_create' %}?production_line={{ line.pk }}" 
                           class="btn btn-outline-primary">
                            إضافة مركز عمل
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- العمود الجانبي -->
        <div>
            <!-- أحدث أوامر الإنتاج -->
            <div class="content-card">
                <h3 class="card-title">
                    <i class="bi bi-clipboard-check"></i>
                    أحدث أوامر الإنتاج
                </h3>
                
                <div class="recent-list">
                    {% for order in recent_orders %}
                    <div class="recent-item">
                        <div class="recent-item-title">
                            <a href="{% url 'production:production_order_detail' order.pk %}" 
                               style="text-decoration: none; color: inherit;">
                                {{ order.order_number }}
                            </a>
                        </div>
                        <div class="recent-item-meta">
                            <span>{{ order.bom.product.name }}</span>
                            <span class="status-badge status-{{ order.status }}">
                                {{ order.get_status_display }}
                            </span>
                        </div>
                    </div>
                    {% empty %}
                    <div class="empty-state">
                        <i class="bi bi-clipboard-check"></i>
                        <p>لا توجد أوامر إنتاج</p>
                        <a href="{% url 'production:production_order_create' %}?production_line={{ line.pk }}" 
                           class="btn btn-outline-primary">
                            إنشاء أمر إنتاج
                        </a>
                    </div>
                    {% endfor %}
                </div>
                
                {% if recent_orders %}
                <div class="text-center mt-3">
                    <a href="{% url 'production:production_order_list' %}?production_line={{ line.pk }}" 
                       class="btn btn-outline-primary btn-sm">
                        عرض جميع الأوامر
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
