from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta
import uuid


class BaseModel(models.Model):
    """نموذج أساسي لجميع النماذج"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='%(class)s_created', verbose_name='أنشئ بواسطة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')

    class Meta:
        abstract = True


class ProductionLine(BaseModel):
    """خطوط الإنتاج"""
    STATUS_CHOICES = [
        ('operational', 'تشغيلي'),
        ('maintenance', 'صيانة'),
        ('idle', 'متوقف'),
        ('setup', 'إعداد'),
    ]

    name = models.CharField(max_length=200, verbose_name='اسم خط الإنتاج')
    code = models.CharField(max_length=50, unique=True, verbose_name='كود خط الإنتاج')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    warehouse = models.ForeignKey('definitions.WarehouseDefinition', on_delete=models.CASCADE,
                                  related_name='production_lines', verbose_name='المخزن')
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='supervised_lines', verbose_name='المشرف')

    # المواصفات التقنية
    capacity_per_hour = models.DecimalField(max_digits=10, decimal_places=2,
                                            validators=[MinValueValidator(0)],
                                            verbose_name='الطاقة الإنتاجية/ساعة')
    efficiency_rate = models.DecimalField(max_digits=5, decimal_places=2, default=85.00,
                                          validators=[MinValueValidator(0), MaxValueValidator(100)],
                                          verbose_name='معدل الكفاءة %')

    # التواريخ والحالة
    installation_date = models.DateField(null=True, blank=True, verbose_name='تاريخ التركيب')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='operational',
                              verbose_name='الحالة')

    # المعلومات المالية
    setup_cost = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True,
                                     validators=[MinValueValidator(0)], verbose_name='تكلفة الإنشاء')
    operating_cost_per_hour = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                                  validators=[MinValueValidator(0)],
                                                  verbose_name='التكلفة التشغيلية/ساعة')

    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'خط إنتاج'
        verbose_name_plural = 'خطوط الإنتاج'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def age_in_years(self):
        """عمر خط الإنتاج بالسنوات"""
        if self.installation_date:
            return (date.today() - self.installation_date).days / 365.25
        return 0

    @property
    def equipment_count(self):
        """عدد المعدات في خط الإنتاج"""
        return self.equipment.filter(is_active=True).count()

    @property
    def active_equipment_count(self):
        """عدد المعدات النشطة"""
        return self.equipment.filter(is_active=True, status='operational').count()


class WorkCenter(BaseModel):
    """مراكز العمل"""
    name = models.CharField(max_length=200, verbose_name='اسم مركز العمل')
    code = models.CharField(max_length=50, unique=True, verbose_name='كود مركز العمل')
    production_line = models.ForeignKey(ProductionLine, on_delete=models.CASCADE,
                                        related_name='work_centers', verbose_name='خط الإنتاج')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')

    # المواصفات
    capacity_per_hour = models.DecimalField(max_digits=10, decimal_places=2,
                                            validators=[MinValueValidator(0)],
                                            verbose_name='الطاقة الإنتاجية/ساعة')
    setup_time_minutes = models.PositiveIntegerField(default=0, verbose_name='وقت الإعداد (دقيقة)')

    # التكاليف
    labor_cost_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0,
                                              validators=[MinValueValidator(0)],
                                              verbose_name='تكلفة العمالة/ساعة')
    overhead_cost_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0,
                                                 validators=[MinValueValidator(0)],
                                                 verbose_name='التكاليف الإضافية/ساعة')

    class Meta:
        verbose_name = 'مركز عمل'
        verbose_name_plural = 'مراكز العمل'
        ordering = ['production_line', 'name']

    def __str__(self):
        return f"{self.name} - {self.production_line.name}"


class BillOfMaterials(BaseModel):
    """قوائم المواد (BOM)"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('active', 'نشطة'),
        ('inactive', 'غير نشطة'),
        ('archived', 'مؤرشفة'),
    ]

    name = models.CharField(max_length=200, verbose_name='اسم قائمة المواد')
    code = models.CharField(max_length=50, unique=True, verbose_name='كود قائمة المواد')
    product = models.ForeignKey('definitions.ProductDefinition', on_delete=models.CASCADE,
                                related_name='bom_lists', verbose_name='المنتج')
    version = models.CharField(max_length=20, default='1.0', verbose_name='الإصدار')

    # الكميات والأوقات
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3, default=1,
                                            validators=[MinValueValidator(0.001)],
                                            verbose_name='الكمية المنتجة')
    production_time_minutes = models.PositiveIntegerField(verbose_name='وقت الإنتاج (دقيقة)')

    # التكاليف
    labor_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0,
                                     validators=[MinValueValidator(0)], verbose_name='تكلفة العمالة')
    overhead_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0,
                                        validators=[MinValueValidator(0)], verbose_name='التكاليف الإضافية')

    # التواريخ والحالة
    effective_date = models.DateField(default=date.today, verbose_name='تاريخ السريان')
    expiry_date = models.DateField(null=True, blank=True, verbose_name='تاريخ الانتهاء')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft',
                              verbose_name='الحالة')

    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'قائمة مواد'
        verbose_name_plural = 'قوائم المواد'
        ordering = ['-effective_date', 'product__name']
        unique_together = ['product', 'version']

    def __str__(self):
        return f"{self.name} - {self.product.name} (v{self.version})"

    @property
    def total_material_cost(self):
        """إجمالي تكلفة المواد"""
        return sum(item.total_cost for item in self.items.all())

    @property
    def total_cost(self):
        """إجمالي التكلفة"""
        return self.total_material_cost + self.labor_cost + self.overhead_cost

    @property
    def unit_cost(self):
        """تكلفة الوحدة"""
        if self.quantity_produced > 0:
            return self.total_cost / self.quantity_produced
        return 0

    @property
    def is_active_now(self):
        """هل قائمة المواد نشطة الآن"""
        today = date.today()
        return (self.status == 'active' and
                self.effective_date <= today and
                (not self.expiry_date or self.expiry_date >= today))


class BOMItem(BaseModel):
    """عناصر قائمة المواد"""
    ITEM_TYPE_CHOICES = [
        ('raw_material', 'مادة خام'),
        ('component', 'مكون'),
        ('sub_assembly', 'تجميع فرعي'),
        ('consumable', 'مادة استهلاكية'),
    ]

    bom = models.ForeignKey(BillOfMaterials, on_delete=models.CASCADE,
                            related_name='items', verbose_name='قائمة المواد')
    material = models.ForeignKey('definitions.ProductDefinition', on_delete=models.CASCADE,
                                 related_name='bom_items', verbose_name='المادة')

    sequence = models.PositiveIntegerField(default=1, verbose_name='التسلسل')
    item_type = models.CharField(max_length=20, choices=ITEM_TYPE_CHOICES,
                                 default='raw_material', verbose_name='نوع العنصر')

    # الكميات والتكاليف
    quantity_required = models.DecimalField(max_digits=10, decimal_places=3,
                                            validators=[MinValueValidator(0.001)],
                                            verbose_name='الكمية المطلوبة')
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0,
                                    validators=[MinValueValidator(0)], verbose_name='تكلفة الوحدة')
    waste_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0,
                                           validators=[MinValueValidator(0), MaxValueValidator(100)],
                                           verbose_name='نسبة الفاقد %')

    # خصائص إضافية
    is_critical = models.BooleanField(default=False, verbose_name='مادة حرجة')
    lead_time_days = models.PositiveIntegerField(default=0, verbose_name='مدة التوريد (يوم)')
    supplier = models.ForeignKey('definitions.PersonDefinition', on_delete=models.SET_NULL,
                                 null=True, blank=True, related_name='bom_items',
                                 verbose_name='المورد')

    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'عنصر قائمة مواد'
        verbose_name_plural = 'عناصر قوائم المواد'
        ordering = ['bom', 'sequence']
        unique_together = ['bom', 'material']

    def __str__(self):
        return f"{self.material.name} - {self.bom.name}"

    @property
    def total_cost(self):
        """إجمالي التكلفة شامل الفاقد"""
        base_cost = self.quantity_required * self.unit_cost
        waste_cost = base_cost * (self.waste_percentage / 100)
        return base_cost + waste_cost

    @property
    def effective_quantity(self):
        """الكمية الفعلية شامل الفاقد"""
        return self.quantity_required * (1 + self.waste_percentage / 100)


class ProductionOrder(BaseModel):
    """أوامر الإنتاج"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('planned', 'مخطط'),
        ('released', 'محرر'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
        ('on_hold', 'معلق'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name='رقم الأمر')
    bom = models.ForeignKey(BillOfMaterials, on_delete=models.CASCADE,
                            related_name='production_orders', verbose_name='قائمة المواد')
    production_line = models.ForeignKey(ProductionLine, on_delete=models.CASCADE,
                                        related_name='production_orders', verbose_name='خط الإنتاج')

    # الكميات والتواريخ
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=3,
                                           validators=[MinValueValidator(0.001)],
                                           verbose_name='الكمية المطلوبة')
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                            validators=[MinValueValidator(0)],
                                            verbose_name='الكمية المنتجة')

    # التواريخ
    planned_start_date = models.DateTimeField(verbose_name='تاريخ البدء المخطط')
    planned_end_date = models.DateTimeField(verbose_name='تاريخ الانتهاء المخطط')
    actual_start_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ البدء الفعلي')
    actual_end_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ الانتهاء الفعلي')

    # الحالة والأولوية
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft',
                              verbose_name='الحالة')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='normal',
                                verbose_name='الأولوية')

    # المسؤولون
    planner = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                related_name='planned_orders', verbose_name='المخطط')
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='supervised_orders', verbose_name='المشرف')

    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'أمر إنتاج'
        verbose_name_plural = 'أوامر الإنتاج'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.bom.product.name}"

    @property
    def completion_percentage(self):
        """نسبة الإنجاز"""
        if self.quantity_ordered > 0:
            return min((self.quantity_produced / self.quantity_ordered) * 100, 100)
        return 0

    @property
    def is_overdue(self):
        """هل الأمر متأخر"""
        if self.status not in ['completed', 'cancelled']:
            return timezone.now() > self.planned_end_date
        return False

    @property
    def days_remaining(self):
        """الأيام المتبقية"""
        if self.status not in ['completed', 'cancelled']:
            delta = self.planned_end_date.date() - date.today()
            return delta.days
        return 0

    def save(self, *args, **kwargs):
        if not self.order_number:
            # إنشاء رقم أمر تلقائي
            today = date.today()
            prefix = f"PO{today.strftime('%Y%m%d')}"
            last_order = ProductionOrder.objects.filter(
                order_number__startswith=prefix
            ).order_by('-order_number').first()

            if last_order:
                last_num = int(last_order.order_number[-3:])
                new_num = last_num + 1
            else:
                new_num = 1

            self.order_number = f"{prefix}{new_num:03d}"

        super().save(*args, **kwargs)
