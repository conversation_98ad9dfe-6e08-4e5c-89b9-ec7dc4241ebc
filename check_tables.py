import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from django.db import connection

cursor = connection.cursor()
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = [row[0] for row in cursor.fetchall()]

print("All tables in database:")
for table in sorted(tables):
    print(f"  - {table}")

print("\nManufacturing tables:")
manufacturing_tables = [table for table in tables if 'manufacturing' in table.lower()]
for table in manufacturing_tables:
    print(f"  - {table}")

if not manufacturing_tables:
    print("  No manufacturing tables found!")
