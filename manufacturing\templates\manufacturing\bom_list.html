{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .search-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .bom-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .bom-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .bom-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s ease;
    }

    .bom-card:hover::before {
        left: 100%;
    }

    .bom-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .bom-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    }

    .bom-name {
        font-size: 1.3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .bom-info {
        margin-bottom: 1rem;
    }

    .bom-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;
        padding: 0.5rem;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 10px;
    }

    .bom-info-label {
        font-weight: 600;
        color: #495057;
    }

    .bom-info-value {
        font-weight: 500;
        color: #2c3e50;
    }

    .btn-group-custom {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .btn-custom {
        flex: 1;
        padding: 0.8rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        text-align: center;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-outline-custom {
        background: transparent;
        border: 2px solid #667eea;
        color: #667eea;
    }

    .btn-outline-custom:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        margin: 2rem 0;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .empty-state-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .empty-state-text {
        color: #6c757d;
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .bom-grid {
            grid-template-columns: 1fr;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .btn-group-custom {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-check me-3"></i>
                    قوائم المواد (BOM)
                </h1>
                <p class="text-white-50 mb-0 mt-2">إدارة قوائم المواد والوصفات الإنتاجية</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:bom_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>قائمة مواد جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label fw-bold">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="اسم قائمة المواد أو المنتج..." 
                       value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>مسودة</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>غير نشط</option>
                    <option value="archived" {% if request.GET.status == 'archived' %}selected{% endif %}>مؤرشف</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" 
                       value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>

    <!-- قائمة قوائم المواد -->
    {% if page_obj %}
    <div class="bom-grid">
        {% for bom in page_obj %}
        <div class="bom-card">
            <div class="bom-header">
                <div class="bom-name">{{ bom.name }}</div>
                <div>
                    {% status_badge bom.status "bom" %}
                </div>
            </div>

            <div class="bom-info">
                <div class="bom-info-item">
                    <span class="bom-info-label">الكود:</span>
                    <span class="bom-info-value">{{ bom.code }}</span>
                </div>
                <div class="bom-info-item">
                    <span class="bom-info-label">المنتج:</span>
                    <span class="bom-info-value">{{ bom.product.name }}</span>
                </div>
                <div class="bom-info-item">
                    <span class="bom-info-label">الإصدار:</span>
                    <span class="bom-info-value">{{ bom.version }}</span>
                </div>
                <div class="bom-info-item">
                    <span class="bom-info-label">الكمية المنتجة:</span>
                    <span class="bom-info-value">{{ bom.quantity_produced|format_quantity }}</span>
                </div>
                <div class="bom-info-item">
                    <span class="bom-info-label">وقت الإنتاج:</span>
                    <span class="bom-info-value">{{ bom.production_time_minutes|time_format }}</span>
                </div>
                <div class="bom-info-item">
                    <span class="bom-info-label">التكلفة الإجمالية:</span>
                    <span class="bom-info-value">{{ bom.total_cost|format_currency }}</span>
                </div>
            </div>

            <div class="btn-group-custom">
                <a href="{% url 'manufacturing:bom_detail' bom.pk %}" 
                   class="btn-custom btn-primary-custom">
                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                </a>
                <a href="#" class="btn-custom btn-outline-custom">
                    <i class="bi bi-pencil me-2"></i>تعديل
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    {% else %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="bi bi-list-x"></i>
        </div>
        <h3 class="empty-state-title">لا توجد قوائم مواد</h3>
        <p class="empty-state-text">لم يتم العثور على قوائم مواد. ابدأ بإنشاء قائمة مواد جديدة.</p>
        <a href="{% url 'manufacturing:bom_create' %}" class="btn btn-primary btn-lg">
            <i class="bi bi-plus-circle me-2"></i>إنشاء قائمة مواد جديدة
        </a>
    </div>
    {% endif %}

    <!-- محتوى إضافي مؤقت -->
    <div class="text-center mt-4" style="background: rgba(255, 255, 255, 0.9); padding: 2rem; border-radius: 20px;">
        <h5 style="color: #2c3e50;">قريباً: ميزات متقدمة</h5>
        <p style="color: #6c757d;">
            • حساب التكاليف التلقائي<br>
            • تحليل المواد المطلوبة<br>
            • تتبع الإصدارات<br>
            • تقارير الاستهلاك
        </p>
    </div>
</div>
{% endblock %}
