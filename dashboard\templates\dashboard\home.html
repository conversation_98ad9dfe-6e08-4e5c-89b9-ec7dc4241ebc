{% extends 'base.html' %}
{% load static %}

{% block title %}{% if user_language == 'en' %}Main Dashboard{% else %}لوحة التحكم الرئيسية{% endif %}{% endblock %}

{% block breadcrumb %}{% if user_language == 'en' %}Dashboard{% else %}لوحة التحكم{% endif %}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .dashboard-container {
        padding: 2rem 0;
    }

    /* Hero Section */
    .hero-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 3rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(45deg, #fff, #f8f9fa);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        opacity: 0.9;
        margin-bottom: 2rem;
    }

    .hero-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .hero-stat {
        text-align: center;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .hero-stat:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.2);
    }

    .hero-stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        color: #fff;
    }

    .hero-stat-label {
        font-size: 0.95rem;
        opacity: 0.8;
    }

    /* Advanced Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .advanced-stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .advanced-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        border-radius: 25px 25px 0 0;
    }

    .advanced-stat-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 60px rgba(31, 38, 135, 0.5);
    }

    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        background: var(--card-gradient);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .stat-trend {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .trend-up { color: #10b981; }
    .trend-down { color: #ef4444; }
    .trend-neutral { color: #6b7280; }

    .stat-main {
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2.8rem;
        font-weight: 800;
        color: #1f2937;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        font-size: 1rem;
        color: #6b7280;
        font-weight: 600;
    }

    .stat-progress {
        margin-top: 1rem;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 10px;
        background: #e5e7eb;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .progress-fill {
        height: 100%;
        border-radius: 10px;
        background: var(--card-gradient);
        transition: width 1s ease;
    }

    .progress-text {
        font-size: 0.8rem;
        color: #6b7280;
        display: flex;
        justify-content: space-between;
    }

    /* Card Gradients */
    .card-customers { --card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .card-suppliers { --card-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .card-products { --card-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .card-warehouses { --card-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    .card-sales { --card-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    .card-purchases { --card-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

    /* Quick Actions Grid */
    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .quick-action-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2.5rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        transition: all 0.4s ease;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .quick-action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--action-gradient);
        border-radius: 25px 25px 0 0;
    }

    .quick-action-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 60px rgba(31, 38, 135, 0.5);
    }

    .action-icon {
        width: 80px;
        height: 80px;
        border-radius: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: white;
        background: var(--action-gradient);
        margin: 0 auto 1.5rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }

    .action-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1rem;
    }

    .action-description {
        color: #6b7280;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .action-btn {
        background: var(--action-gradient);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Action Gradients */
    .action-definitions { --action-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .action-warehouses { --action-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .action-sales { --action-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .action-purchases { --action-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    .action-manufacturing { --action-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    .action-assets { --action-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
    .action-banks { --action-gradient: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
    .action-reports { --action-gradient: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }

    /* Advanced Dashboard Widgets */
    .dashboard-widgets {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .widget {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
    }

    .widget-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f3f4f6;
    }

    .widget-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .widget-action {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .widget-action:hover {
        color: #764ba2;
        text-decoration: none;
    }

    /* Recent Activities */
    .activity-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-radius: 15px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .activity-item:hover {
        background: #f8fafc;
        border-color: #e2e8f0;
        transform: translateX(5px);
    }

    .activity-icon {
        width: 45px;
        height: 45px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .activity-time {
        font-size: 0.8rem;
        color: #6b7280;
    }

    /* System Status */
    .status-grid {
        display: grid;
        gap: 1rem;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border-radius: 15px;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
    }

    .status-label {
        font-weight: 600;
        color: #374151;
    }

    .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
    }

    .status-online { background: #10b981; }
    .status-warning { background: #f59e0b; }
    .status-error { background: #ef4444; }

    /* Chart Styles */
    .chart-tabs {
        display: flex;
        gap: 0.5rem;
        border-bottom: 2px solid #f3f4f6;
        padding-bottom: 1rem;
    }

    .chart-tab {
        background: transparent;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .chart-tab.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .chart-tab:hover:not(.active) {
        background: #f3f4f6;
        color: #374151;
    }

    .chart-container {
        background: #f8fafc;
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .chart-legend {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        font-weight: 600;
        color: #374151;
    }

    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: block;
    }

    .chart-period {
        transition: all 0.3s ease;
    }

    .chart-period.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-color: transparent !important;
        color: white !important;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .dashboard-widgets {
            grid-template-columns: 1fr;
        }

        .chart-tabs {
            flex-wrap: wrap;
        }

        .chart-legend {
            gap: 1rem;
        }
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .quick-actions-grid {
            grid-template-columns: 1fr;
        }

        .hero-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .chart-tabs {
            gap: 0.25rem;
        }

        .chart-tab {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }

        .chart-legend {
            gap: 1rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="dashboard-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">
                        <i class="bi bi-speedometer2 me-3"></i>
                        {% if user_language == 'en' %}Welcome to Osaric{% else %}مرحباً بك في أوساريك{% endif %}
                    </h1>
                    <p class="hero-subtitle">
                        {% if user_language == 'en' %}Integrated Business Management System - Smart dashboard to manage all your business operations{% else %}نظام إدارة الأعمال المتكامل - لوحة التحكم الذكية لإدارة جميع عملياتك التجارية{% endif %}
                    </p>
                    <div class="d-flex gap-3 mt-3">
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="bi bi-shield-check me-2"></i>{% if user_language == 'en' %}Secure & Protected{% else %}آمن ومحمي{% endif %}
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="bi bi-lightning me-2"></i>{% if user_language == 'en' %}Fast & Efficient{% else %}سريع وفعال{% endif %}
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="bi bi-graph-up me-2"></i>{% if user_language == 'en' %}Advanced Analytics{% else %}تحليلات متقدمة{% endif %}
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="hero-stats">
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ customers_count|add:suppliers_count }}</div>
                            <div class="hero-stat-label">{% if user_language == 'en' %}Total Customers & Suppliers{% else %}إجمالي العملاء والموردين{% endif %}</div>
                        </div>
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ products_count }}</div>
                            <div class="hero-stat-label">{% if user_language == 'en' %}Registered Products{% else %}المنتجات المسجلة{% endif %}</div>
                        </div>
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ warehouses_count }}</div>
                            <div class="hero-stat-label">{% if user_language == 'en' %}Active Warehouses{% else %}المخازن النشطة{% endif %}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Statistics Cards -->
    <div class="stats-grid">
        <div class="advanced-stat-card card-customers">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stat-trend trend-up">
                    <i class="bi bi-arrow-up"></i>
                    +12%
                </div>
            </div>
            <div class="stat-main">
                <div class="stat-number">{{ customers_count }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Registered Customers{% else %}العملاء المسجلين{% endif %}</div>
            </div>
            <div class="stat-progress">
                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: 75%;"></div>
                </div>
                <div class="progress-text">
                    <span>{% if user_language == 'en' %}Active: {{ customers_count }}{% else %}نشط: {{ customers_count }}{% endif %}</span>
                    <span>75%</span>
                </div>
            </div>
        </div>

        <div class="advanced-stat-card card-suppliers">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-truck"></i>
                </div>
                <div class="stat-trend trend-up">
                    <i class="bi bi-arrow-up"></i>
                    +8%
                </div>
            </div>
            <div class="stat-main">
                <div class="stat-number">{{ suppliers_count }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Active Suppliers{% else %}الموردين النشطين{% endif %}</div>
            </div>
            <div class="stat-progress">
                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: 60%;"></div>
                </div>
                <div class="progress-text">
                    <span>{% if user_language == 'en' %}Active: {{ suppliers_count }}{% else %}نشط: {{ suppliers_count }}{% endif %}</span>
                    <span>60%</span>
                </div>
            </div>
        </div>

        <div class="advanced-stat-card card-products">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-box-seam"></i>
                </div>
                <div class="stat-trend trend-up">
                    <i class="bi bi-arrow-up"></i>
                    +25%
                </div>
            </div>
            <div class="stat-main">
                <div class="stat-number">{{ products_count }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Available Products{% else %}المنتجات المتاحة{% endif %}</div>
            </div>
            <div class="stat-progress">
                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: 90%;"></div>
                </div>
                <div class="progress-text">
                    <span>{% if user_language == 'en' %}Available: {{ products_count }}{% else %}متوفر: {{ products_count }}{% endif %}</span>
                    <span>90%</span>
                </div>
            </div>
        </div>

        <div class="advanced-stat-card card-warehouses">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-building"></i>
                </div>
                <div class="stat-trend trend-neutral">
                    <i class="bi bi-dash"></i>
                    0%
                </div>
            </div>
            <div class="stat-main">
                <div class="stat-number">{{ warehouses_count }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Active Warehouses{% else %}المخازن النشطة{% endif %}</div>
            </div>
            <div class="stat-progress">
                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: 100%;"></div>
                </div>
                <div class="progress-text">
                    <span>{% if user_language == 'en' %}Active: {{ warehouses_count }}{% else %}نشط: {{ warehouses_count }}{% endif %}</span>
                    <span>100%</span>
                </div>
            </div>
        </div>

        <div class="advanced-stat-card card-sales">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-currency-exchange"></i>
                </div>
                <div class="stat-trend trend-up">
                    <i class="bi bi-arrow-up"></i>
                    +18%
                </div>
            </div>
            <div class="stat-main">
                <div class="stat-number">{{ currencies_count }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Supported Currencies{% else %}العملات المدعومة{% endif %}</div>
            </div>
            <div class="stat-progress">
                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: 85%;"></div>
                </div>
                <div class="progress-text">
                    <span>{% if user_language == 'en' %}Active: {{ currencies_count }}{% else %}نشط: {{ currencies_count }}{% endif %}</span>
                    <span>85%</span>
                </div>
            </div>
        </div>

        <div class="advanced-stat-card card-purchases">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-bank"></i>
                </div>
                <div class="stat-trend trend-neutral">
                    <i class="bi bi-dash"></i>
                    0%
                </div>
            </div>
            <div class="stat-main">
                <div class="stat-number">{{ banks_count }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Registered Banks{% else %}البنوك المسجلة{% endif %}</div>
            </div>
            <div class="stat-progress">
                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: 70%;"></div>
                </div>
                <div class="progress-text">
                    <span>{% if user_language == 'en' %}Active: {{ banks_count }}{% else %}نشط: {{ banks_count }}{% endif %}</span>
                    <span>70%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Quick Actions -->
    <div class="quick-actions-grid">
        <div class="quick-action-card action-definitions">
            <div class="action-icon">
                <i class="bi bi-gear-fill"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Definitions{% else %}التعريفات{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Manage all basic system definitions including customers, suppliers, products and warehouses{% else %}إدارة جميع التعريفات الأساسية للنظام من عملاء وموردين ومنتجات ومخازن{% endif %}</p>
            <a href="{% url 'definitions:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}Manage Definitions{% else %}إدارة التعريفات{% endif %}
            </a>
        </div>

        <div class="quick-action-card action-warehouses">
            <div class="action-icon">
                <i class="bi bi-boxes"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Warehouse Management{% else %}إدارة المخازن{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Track inventory, goods movement, counting and transfers between warehouses{% else %}متابعة المخزون وحركة البضائع والجرد والتحويلات بين المخازن{% endif %}</p>
            <a href="{% url 'warehouses:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}Manage Warehouses{% else %}إدارة المخازن{% endif %}
            </a>
        </div>

        <div class="quick-action-card action-sales">
            <div class="action-icon">
                <i class="bi bi-cart-plus"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Sales{% else %}المبيعات{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Manage sales invoices, quotes, customers and track business performance{% else %}إدارة فواتير المبيعات والعروض والعملاء وتتبع الأداء التجاري{% endif %}</p>
            <a href="{% url 'sales:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}Manage Sales{% else %}إدارة المبيعات{% endif %}
            </a>
        </div>

        <div class="quick-action-card action-purchases">
            <div class="action-icon">
                <i class="bi bi-cart-check"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Purchases{% else %}المشتريات{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Manage purchase orders, suppliers, purchase invoices and payments{% else %}إدارة طلبات الشراء والموردين وفواتير المشتريات والمدفوعات{% endif %}</p>
            <a href="{% url 'purchases:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}Manage Purchases{% else %}إدارة المشتريات{% endif %}
            </a>
        </div>

        <div class="quick-action-card action-manufacturing">
            <div class="action-icon">
                <i class="bi bi-gear-wide-connected"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Manufacturing{% else %}التصنيع{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Manage production operations, manufacturing, production lines and quality control{% else %}إدارة عمليات الإنتاج والتصنيع وخطوط الإنتاج ومراقبة الجودة{% endif %}</p>
            <a href="{% url 'production:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}Manage Production{% else %}إدارة الإنتاج{% endif %}
            </a>
        </div>

        <div class="quick-action-card action-assets">
            <div class="action-icon">
                <i class="bi bi-building"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Fixed Assets{% else %}الأصول الثابتة{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Manage fixed assets, depreciation, maintenance and track asset values{% else %}إدارة الأصول الثابتة والاستهلاك والصيانة وتتبع قيمة الأصول{% endif %}</p>
            <a href="{% url 'assets:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}Manage Assets{% else %}إدارة الأصول{% endif %}
            </a>
        </div>

        <div class="quick-action-card action-banks">
            <div class="action-icon">
                <i class="bi bi-bank"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Banks & Treasuries{% else %}البنوك والخزائن{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Manage bank accounts, treasuries, financial transactions and transfers{% else %}إدارة الحسابات البنكية والخزائن والمعاملات المالية والتحويلات{% endif %}</p>
            <a href="{% url 'banks:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}Manage Banks{% else %}إدارة البنوك{% endif %}
            </a>
        </div>

        <div class="quick-action-card action-reports">
            <div class="action-icon">
                <i class="bi bi-graph-up"></i>
            </div>
            <h3 class="action-title">{% if user_language == 'en' %}Reports{% else %}التقارير{% endif %}</h3>
            <p class="action-description">{% if user_language == 'en' %}Comprehensive and detailed reports for all operations with advanced analytics and charts{% else %}تقارير شاملة ومفصلة لجميع العمليات مع تحليلات متقدمة ورسوم بيانية{% endif %}</p>
            <a href="{% url 'reports:dashboard' %}" class="action-btn">
                <i class="bi bi-arrow-left"></i>
                {% if user_language == 'en' %}View Reports{% else %}عرض التقارير{% endif %}
            </a>
        </div>
    </div>

    <!-- Advanced Dashboard Widgets -->
    <div class="dashboard-widgets">
        <div class="widget">
            <div class="widget-header">
                <h3 class="widget-title">
                    <i class="bi bi-activity"></i>
                    {% if user_language == 'en' %}Recent Activities{% else %}النشاطات الأخيرة{% endif %}
                </h3>
                <a href="#" class="widget-action">{% if user_language == 'en' %}View All{% else %}عرض الكل{% endif %}</a>
            </div>

            <div class="activity-item">
                <div class="activity-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="bi bi-plus-circle"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">{% if user_language == 'en' %}New product added{% else %}تم إضافة منتج جديد{% endif %}</div>
                    <div class="activity-time">{% if user_language == 'en' %}Dell XPS 13 Laptop - 2 hours ago{% else %}لابتوب ديل XPS 13 - منذ ساعتين{% endif %}</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <i class="bi bi-arrow-up-circle"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">{% if user_language == 'en' %}Inventory update{% else %}تحديث مخزون{% endif %}</div>
                    <div class="activity-time">{% if user_language == 'en' %}Wireless Mouse - Added 50 pieces - 4 hours ago{% else %}ماوس لاسلكي - تم إضافة 50 قطعة - منذ 4 ساعات{% endif %}</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="bi bi-person-plus"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">{% if user_language == 'en' %}New customer{% else %}عميل جديد{% endif %}</div>
                    <div class="activity-time">{% if user_language == 'en' %}Advanced Technology Company - Yesterday{% else %}شركة التقنية المتقدمة - أمس{% endif %}</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="bi bi-cart-check"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">{% if user_language == 'en' %}New purchase order{% else %}طلب شراء جديد{% endif %}</div>
                    <div class="activity-time">{% if user_language == 'en' %}Order #1234 - Value $15,000 - Yesterday{% else %}طلب رقم #1234 - قيمة 15,000 ريال - أمس{% endif %}</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">{% if user_language == 'en' %}Stock alert{% else %}تنبيه مخزون{% endif %}</div>
                    <div class="activity-time">{% if user_language == 'en' %}5 products below minimum level - 2 days ago{% else %}5 منتجات تحت الحد الأدنى - منذ يومين{% endif %}</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                    <i class="bi bi-cash-coin"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">{% if user_language == 'en' %}New payment{% else %}دفعة جديدة{% endif %}</div>
                    <div class="activity-time">{% if user_language == 'en' %}Payment from customer Ahmed Mohamed - $8,500 - 3 days ago{% else %}دفعة من العميل أحمد محمد - 8,500 ريال - منذ 3 أيام{% endif %}</div>
                </div>
            </div>
        </div>
        <div class="widget">
            <div class="widget-header">
                <h3 class="widget-title">
                    <i class="bi bi-shield-check"></i>
                    {% if user_language == 'en' %}System Status{% else %}حالة النظام{% endif %}
                </h3>
                <a href="#" class="widget-action">{% if user_language == 'en' %}Details{% else %}التفاصيل{% endif %}</a>
            </div>

            <div class="status-grid">
                <div class="status-item">
                    <span class="status-label">{% if user_language == 'en' %}Server Status{% else %}حالة الخادم{% endif %}</span>
                    <div class="status-indicator">
                        <span class="status-dot status-online"></span>
                        {% if user_language == 'en' %}Connected{% else %}متصل{% endif %}
                    </div>
                </div>

                <div class="status-item">
                    <span class="status-label">{% if user_language == 'en' %}Database{% else %}قاعدة البيانات{% endif %}</span>
                    <div class="status-indicator">
                        <span class="status-dot status-online"></span>
                        {% if user_language == 'en' %}Active{% else %}نشطة{% endif %}
                    </div>
                </div>

                <div class="status-item">
                    <span class="status-label">{% if user_language == 'en' %}Backup{% else %}النسخ الاحتياطي{% endif %}</span>
                    <div class="status-indicator">
                        <span class="status-dot status-warning"></span>
                        {% if user_language == 'en' %}Last backup: Yesterday{% else %}آخر نسخة: أمس{% endif %}
                    </div>
                </div>

                <div class="status-item">
                    <span class="status-label">{% if user_language == 'en' %}Storage Space{% else %}مساحة التخزين{% endif %}</span>
                    <div class="status-indicator">
                        <span class="status-dot status-online"></span>
                        {% if user_language == 'en' %}75% Available{% else %}75% متاح{% endif %}
                    </div>
                </div>

                <div class="status-item">
                    <span class="status-label">{% if user_language == 'en' %}Active Users{% else %}المستخدمين النشطين{% endif %}</span>
                    <div class="status-indicator">
                        <span class="status-dot status-online"></span>
                        {% if user_language == 'en' %}3 Users{% else %}3 مستخدمين{% endif %}
                    </div>
                </div>

                {% if low_stock_count > 0 %}
                <div class="status-item">
                    <span class="status-label">{% if user_language == 'en' %}Stock Alerts{% else %}تنبيهات المخزون{% endif %}</span>
                    <div class="status-indicator">
                        <span class="status-dot status-error"></span>
                        {% if user_language == 'en' %}{{ low_stock_count }} Products{% else %}{{ low_stock_count }} منتج{% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <div style="margin-top: 2rem;">
                <a href="{% url 'definitions:dashboard' %}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="bi bi-gear"></i>
                    {% if user_language == 'en' %}System Management{% else %}إدارة النظام{% endif %}
                </a>
            </div>
        </div>
    </div>

    <!-- Performance Chart Section -->
    <div class="widget" style="grid-column: 1 / -1; margin-top: 2rem;">
        <div class="widget-header">
            <h3 class="widget-title">
                <i class="bi bi-graph-up"></i>
                {% if user_language == 'en' %}System Performance{% else %}أداء النظام{% endif %}
            </h3>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary chart-period" data-period="daily">{% if user_language == 'en' %}Daily{% else %}يومي{% endif %}</button>
                <button class="btn btn-sm btn-primary chart-period active" data-period="weekly">{% if user_language == 'en' %}Weekly{% else %}أسبوعي{% endif %}</button>
                <button class="btn btn-sm btn-outline-primary chart-period" data-period="monthly">{% if user_language == 'en' %}Monthly{% else %}شهري{% endif %}</button>
            </div>
        </div>

        <!-- Chart Tabs -->
        <div class="chart-tabs" style="margin: 1rem 0;">
            <button class="chart-tab active" data-chart="performance">{% if user_language == 'en' %}Overall Performance{% else %}الأداء العام{% endif %}</button>
            <button class="chart-tab" data-chart="sales">{% if user_language == 'en' %}Sales{% else %}المبيعات{% endif %}</button>
            <button class="chart-tab" data-chart="inventory">{% if user_language == 'en' %}Inventory{% else %}المخزون{% endif %}</button>
            <button class="chart-tab" data-chart="financial">{% if user_language == 'en' %}Financial{% else %}المالية{% endif %}</button>
        </div>

        <!-- Performance Chart -->
        <div class="chart-container" id="performanceChart" style="display: block;">
            <canvas id="performanceCanvas" style="height: 350px;"></canvas>
        </div>

        <!-- Sales Chart -->
        <div class="chart-container" id="salesChart" style="display: none;">
            <canvas id="salesCanvas" style="height: 350px;"></canvas>
        </div>

        <!-- Inventory Chart -->
        <div class="chart-container" id="inventoryChart" style="display: none;">
            <canvas id="inventoryCanvas" style="height: 350px;"></canvas>
        </div>

        <!-- Financial Chart -->
        <div class="chart-container" id="financialChart" style="display: none;">
            <canvas id="financialCanvas" style="height: 350px;"></canvas>
        </div>

        <!-- Chart Legend -->
        <div class="chart-legend" style="margin-top: 1rem; display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap;">
            <div class="legend-item">
                <span class="legend-color" style="background: linear-gradient(135deg, #667eea, #764ba2);"></span>
                <span>{% if user_language == 'en' %}Sales{% else %}المبيعات{% endif %}</span>
                <span class="legend-value" id="salesValue" style="margin-right: 0.5rem; font-weight: bold; color: #667eea;">0</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: linear-gradient(135deg, #f093fb, #f5576c);"></span>
                <span>{% if user_language == 'en' %}Purchases{% else %}المشتريات{% endif %}</span>
                <span class="legend-value" id="purchasesValue" style="margin-right: 0.5rem; font-weight: bold; color: #f093fb;">0</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: linear-gradient(135deg, #4facfe, #00f2fe);"></span>
                <span>{% if user_language == 'en' %}Profits{% else %}الأرباح{% endif %}</span>
                <span class="legend-value" id="profitValue" style="margin-right: 0.5rem; font-weight: bold; color: #4facfe;">0</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: linear-gradient(135deg, #43e97b, #38f9d7);"></span>
                <span>{% if user_language == 'en' %}Inventory{% else %}المخزون{% endif %}</span>
                <span class="legend-value" id="inventoryValue" style="margin-right: 0.5rem; font-weight: bold; color: #43e97b;">{{ products_count }}</span>
            </div>
        </div>

        <!-- Real-time Performance Indicators -->
        <div style="margin-top: 2rem; padding: 1.5rem; background: rgba(102, 126, 234, 0.05); border-radius: 15px; border: 1px solid rgba(102, 126, 234, 0.1);">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div style="text-align: center;">
                    <div style="font-size: 0.85rem; color: #6b7280; margin-bottom: 0.5rem;">{% if user_language == 'en' %}Daily Growth Rate{% else %}معدل النمو اليومي{% endif %}</div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #10b981;" id="growthRate">+12.5%</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 0.85rem; color: #6b7280; margin-bottom: 0.5rem;">{% if user_language == 'en' %}Average Order Value{% else %}متوسط قيمة الطلب{% endif %}</div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #667eea;" id="avgOrderValue">{% if user_language == 'en' %}$2,450{% else %}2,450 ريال{% endif %}</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 0.85rem; color: #6b7280; margin-bottom: 0.5rem;">{% if user_language == 'en' %}Inventory Turnover{% else %}معدل دوران المخزون{% endif %}</div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #f59e0b;" id="inventoryTurnover">4.2x</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 0.85rem; color: #6b7280; margin-bottom: 0.5rem;">{% if user_language == 'en' %}Customer Satisfaction{% else %}رضا العملاء{% endif %}</div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #8b5cf6;" id="customerSatisfaction">94%</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 {% if user_language == "en" %}Advanced dashboard loaded{% else %}تم تحميل لوحة التحكم المتقدمة{% endif %}');

    // تأثيرات تحميل للبطاقات
    const cards = document.querySelectorAll('.advanced-stat-card, .quick-action-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // تحريك شريط التقدم
    setTimeout(() => {
        const progressBars = document.querySelectorAll('.progress-fill');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.transition = 'width 1.5s ease';
                bar.style.width = width;
            }, 500);
        });
    }, 1000);

    // تأثير النقر على البطاقات
    document.querySelectorAll('.advanced-stat-card, .quick-action-card').forEach(card => {
        card.addEventListener('click', function(e) {
            if (!e.target.closest('a')) {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            }
        });
    });

    // إعداد الرسوم البيانية
    let charts = {};

    // بيانات وهمية للرسوم البيانية
    const chartData = {
        performance: {
            daily: {
                labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                datasets: [{
                    label: 'المبيعات',
                    data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'المشتريات',
                    data: [8000, 12000, 10000, 18000, 15000, 20000, 18000],
                    borderColor: '#f093fb',
                    backgroundColor: 'rgba(240, 147, 251, 0.1)',
                    tension: 0.4
                }]
            },
            weekly: {
                labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                datasets: [{
                    label: 'المبيعات',
                    data: [85000, 92000, 78000, 105000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'المشتريات',
                    data: [65000, 70000, 58000, 80000],
                    borderColor: '#f093fb',
                    backgroundColor: 'rgba(240, 147, 251, 0.1)',
                    tension: 0.4
                }]
            },
            monthly: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'المبيعات',
                    data: [320000, 380000, 290000, 420000, 350000, 480000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'المشتريات',
                    data: [250000, 280000, 220000, 320000, 270000, 360000],
                    borderColor: '#f093fb',
                    backgroundColor: 'rgba(240, 147, 251, 0.1)',
                    tension: 0.4
                }]
            }
        },
        sales: {
            weekly: {
                labels: ['منتجات إلكترونية', 'ملابس', 'أثاث', 'كتب', 'رياضة'],
                datasets: [{
                    data: [35, 25, 20, 12, 8],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(240, 147, 251, 0.8)',
                        'rgba(79, 172, 254, 0.8)',
                        'rgba(67, 233, 123, 0.8)',
                        'rgba(250, 112, 154, 0.8)'
                    ],
                    borderWidth: 0
                }]
            }
        },
        inventory: {
            weekly: {
                labels: ['متوفر', 'قليل', 'نفد', 'قيد الطلب'],
                datasets: [{
                    data: [65, 15, 8, 12],
                    backgroundColor: [
                        'rgba(67, 233, 123, 0.8)',
                        'rgba(250, 204, 21, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(156, 163, 175, 0.8)'
                    ],
                    borderWidth: 0
                }]
            }
        },
        financial: {
            weekly: {
                labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                datasets: [{
                    label: 'الإيرادات',
                    data: [85000, 92000, 78000, 105000],
                    backgroundColor: 'rgba(67, 233, 123, 0.8)',
                }, {
                    label: 'المصروفات',
                    data: [65000, 70000, 58000, 80000],
                    backgroundColor: 'rgba(239, 68, 68, 0.8)',
                }, {
                    label: 'الأرباح',
                    data: [20000, 22000, 20000, 25000],
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                }]
            }
        }
    };

    // إنشاء الرسوم البيانية
    function createChart(canvasId, type, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 10,
                    displayColors: true
                }
            },
            scales: type !== 'doughnut' && type !== 'pie' ? {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(107, 114, 128, 0.1)'
                    },
                    ticks: {
                        color: '#6b7280'
                    }
                }
            } : {}
        };

        return new Chart(ctx, {
            type: type,
            data: data,
            options: { ...defaultOptions, ...options }
        });
    }

    // إنشاء الرسوم البيانية الأولية
    charts.performance = createChart('performanceCanvas', 'line', chartData.performance.weekly);
    charts.sales = createChart('salesCanvas', 'doughnut', chartData.sales.weekly);
    charts.inventory = createChart('inventoryCanvas', 'pie', chartData.inventory.weekly);
    charts.financial = createChart('financialCanvas', 'bar', chartData.financial.weekly);

    // التبديل بين التبويبات
    document.querySelectorAll('.chart-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.chart-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.chart-container').forEach(c => c.style.display = 'none');

            // إضافة الفئة النشطة للتبويب المحدد
            this.classList.add('active');
            const chartType = this.dataset.chart;
            document.getElementById(chartType + 'Chart').style.display = 'block';
        });
    });

    // التبديل بين الفترات الزمنية
    document.querySelectorAll('.chart-period').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.chart-period').forEach(b => {
                b.classList.remove('active', 'btn-primary');
                b.classList.add('btn-outline-primary');
            });

            this.classList.remove('btn-outline-primary');
            this.classList.add('active', 'btn-primary');

            const period = this.dataset.period;
            const activeTab = document.querySelector('.chart-tab.active').dataset.chart;

            if (activeTab === 'performance' && chartData.performance[period]) {
                charts.performance.data = chartData.performance[period];
                charts.performance.update('active');
            }
        });
    });

    // تحديث المؤشرات في الوقت الفعلي
    function updateRealTimeIndicators() {
        // تحديث قيم الأسطورة
        const salesValue = document.getElementById('salesValue');
        const purchasesValue = document.getElementById('purchasesValue');
        const profitValue = document.getElementById('profitValue');
        const growthRate = document.getElementById('growthRate');
        const avgOrderValue = document.getElementById('avgOrderValue');
        const inventoryTurnover = document.getElementById('inventoryTurnover');
        const customerSatisfaction = document.getElementById('customerSatisfaction');

        if (salesValue) {
            const currentSales = parseInt(salesValue.textContent) || 105000;
            const newSales = Math.round(currentSales * (1 + (Math.random() - 0.5) * 0.02));
            salesValue.textContent = newSales.toLocaleString() + ' ريال';
        }

        if (purchasesValue) {
            const currentPurchases = parseInt(purchasesValue.textContent) || 80000;
            const newPurchases = Math.round(currentPurchases * (1 + (Math.random() - 0.5) * 0.02));
            purchasesValue.textContent = newPurchases.toLocaleString() + ' ريال';
        }

        if (profitValue) {
            const sales = parseInt(salesValue?.textContent) || 105000;
            const purchases = parseInt(purchasesValue?.textContent) || 80000;
            const profit = sales - purchases;
            profitValue.textContent = profit.toLocaleString() + ' ريال';
        }

        if (growthRate) {
            const rates = ['+12.5%', '+13.2%', '+11.8%', '+14.1%', '+12.9%'];
            growthRate.textContent = rates[Math.floor(Math.random() * rates.length)];
        }

        if (avgOrderValue) {
            const baseValue = 2450;
            const newValue = Math.round(baseValue * (1 + (Math.random() - 0.5) * 0.1));
            const currency = '{% if user_language == "en" %}${% else %}ريال{% endif %}';
            const currencyPosition = '{% if user_language == "en" %}before{% else %}after{% endif %}';

            if (currencyPosition === 'before') {
                avgOrderValue.textContent = currency + newValue.toLocaleString();
            } else {
                avgOrderValue.textContent = newValue.toLocaleString() + ' ' + currency;
            }
        }

        if (inventoryTurnover) {
            const rates = ['4.2x', '4.1x', '4.3x', '4.0x', '4.4x'];
            inventoryTurnover.textContent = rates[Math.floor(Math.random() * rates.length)];
        }

        if (customerSatisfaction) {
            const rates = ['94%', '95%', '93%', '96%', '94%'];
            customerSatisfaction.textContent = rates[Math.floor(Math.random() * rates.length)];
        }
    }

    // إضافة تأثيرات تفاعلية للرسوم البيانية
    setTimeout(() => {
        // تحديث أولي للمؤشرات
        updateRealTimeIndicators();

        // تحديث البيانات كل 15 ثانية (محاكاة البيانات الحية)
        setInterval(() => {
            updateRealTimeIndicators();

            if (charts.performance && charts.performance.data) {
                const datasets = charts.performance.data.datasets;
                datasets.forEach(dataset => {
                    dataset.data = dataset.data.map(value => {
                        const variation = (Math.random() - 0.5) * 0.05; // تغيير بنسبة ±2.5%
                        return Math.max(0, Math.round(value * (1 + variation)));
                    });
                });
                charts.performance.update('none');
            }
        }, 15000);

        // إضافة تأثير النقر على الرسوم البيانية
        Object.values(charts).forEach(chart => {
            if (chart && chart.canvas) {
                chart.canvas.addEventListener('click', function() {
                    console.log('{% if user_language == "en" %}Chart clicked{% else %}تم النقر على الرسم البياني{% endif %}');
                    // يمكن إضافة المزيد من التفاعل هنا
                });
            }
        });
    }, 2000);

    console.log('📊 {% if user_language == "en" %}Interactive charts loaded{% else %}تم تحميل الرسوم البيانية التفاعلية{% endif %}');
});
</script>
</div>
{% endblock %}
