{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .status-badge {
        padding: 0.4rem 1rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
    }
    .status-draft { background: #6c757d; color: white; }
    .status-planned { background: #17a2b8; color: white; }
    .status-released { background: #ffc107; color: black; }
    .status-in_progress { background: #007bff; color: white; }
    .status-completed { background: #28a745; color: white; }
    .status-cancelled { background: #dc3545; color: white; }
    .status-on_hold { background: #fd7e14; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-clipboard-check me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'production:production_order_create' %}" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>إنشاء جديد
                </a>
            </div>
        </div>
    </div>

    <div class="content-card">
        <form method="get" class="row g-3 mb-4">
            <div class="col-md-3">
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث..." value="{{ search_query }}">
            </div>
            <div class="col-md-2">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select name="priority" class="form-select">
                    <option value="">جميع الأولويات</option>
                    {% for value, label in priority_choices %}
                    <option value="{{ value }}" {% if priority_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select name="production_line" class="form-select">
                    <option value="">جميع خطوط الإنتاج</option>
                    {% for line in production_lines %}
                    <option value="{{ line.id }}" {% if line_filter == line.id|stringformat:"s" %}selected{% endif %}>
                        {{ line.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">بحث</button>
            </div>
        </form>

        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم الأمر</th>
                        <th>المنتج</th>
                        <th>خط الإنتاج</th>
                        <th>الكمية المطلوبة</th>
                        <th>الكمية المنتجة</th>
                        <th>نسبة الإنجاز</th>
                        <th>الحالة</th>
                        <th>الأولوية</th>
                        <th>تاريخ البدء المخطط</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in page_obj %}
                    <tr>
                        <td>
                            <a href="{% url 'production:production_order_detail' order.pk %}">
                                {{ order.order_number }}
                            </a>
                        </td>
                        <td>{{ order.bom.product.name }}</td>
                        <td>{{ order.production_line.name }}</td>
                        <td>{{ order.quantity_ordered }}</td>
                        <td>{{ order.quantity_produced }}</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ order.completion_percentage }}%">
                                    {{ order.completion_percentage|floatformat:1 }}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-{{ order.status }}">
                                {{ order.get_status_display }}
                            </span>
                        </td>
                        <td>{{ order.get_priority_display }}</td>
                        <td>{{ order.planned_start_date|date:"Y-m-d H:i" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'production:production_order_detail' order.pk %}" 
                                   class="btn btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'production:production_order_update' order.pk %}" 
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- الترقيم -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابق</a>
                </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">{{ page_obj.number }}</span>
                </li>
                
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-clipboard-check" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3">لا توجد أوامر إنتاج</h3>
            <p class="text-muted">لم يتم العثور على أوامر إنتاج تطابق معايير البحث</p>
            <a href="{% url 'production:production_order_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>إنشاء أمر إنتاج جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
