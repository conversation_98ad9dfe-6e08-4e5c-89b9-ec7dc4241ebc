from django.contrib import admin
from .models import (
    ProductionLine, BillOfMaterials, BOMItem, ProductionOrder,
    ProductionOrderItem, QualityCheck, ProductionReport,
    Equipment, EquipmentCategory, MaintenanceType, MaintenanceSchedule,
    MaintenanceRecord, PerformanceMetric, DowntimeRecord
)


@admin.register(ProductionLine)
class ProductionLineAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'warehouse', 'capacity_per_hour', 'efficiency_rate', 'status', 'is_active']
    list_filter = ['status', 'is_active', 'warehouse']
    search_fields = ['name', 'code']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(BillOfMaterials)
class BillOfMaterialsAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'product', 'version', 'status', 'effective_date']
    list_filter = ['status', 'effective_date']
    search_fields = ['name', 'code', 'product__name']
    readonly_fields = ['created_at', 'updated_at', 'total_cost']


@admin.register(BOMItem)
class BOMItemAdmin(admin.ModelAdmin):
    list_display = ['bom', 'material', 'sequence', 'quantity_required', 'unit_cost', 'is_critical']
    list_filter = ['item_type', 'is_critical']
    search_fields = ['bom__name', 'material__name']


@admin.register(ProductionOrder)
class ProductionOrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'bom', 'production_line', 'quantity_ordered', 'status', 'priority']
    list_filter = ['status', 'priority', 'production_line']
    search_fields = ['order_number', 'bom__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(QualityCheck)
class QualityCheckAdmin(admin.ModelAdmin):
    list_display = ['production_order', 'check_type', 'status', 'inspector', 'check_date']
    list_filter = ['check_type', 'status', 'check_date']
    search_fields = ['production_order__order_number']


@admin.register(ProductionReport)
class ProductionReportAdmin(admin.ModelAdmin):
    list_display = ['production_order', 'report_date', 'shift', 'quantity_produced', 'efficiency_rate']
    list_filter = ['shift', 'report_date']
    search_fields = ['production_order__order_number']


# إدارة المعدات
@admin.register(EquipmentCategory)
class EquipmentCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'is_active', 'created_at']
    list_filter = ['is_active']
    search_fields = ['name', 'code']


@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'category', 'production_line', 'status', 'condition', 'needs_maintenance']
    list_filter = ['status', 'condition', 'category', 'production_line', 'is_active']
    search_fields = ['name', 'code', 'manufacturer', 'model', 'serial_number']
    readonly_fields = ['created_at', 'updated_at', 'needs_maintenance', 'days_until_maintenance', 'age_in_years']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'code', 'category', 'production_line', 'is_active')
        }),
        ('المواصفات الفنية', {
            'fields': ('manufacturer', 'model', 'serial_number', 'year_manufactured', 
                      'rated_capacity', 'capacity_unit', 'power_consumption')
        }),
        ('الحالة والصيانة', {
            'fields': ('status', 'condition', 'installation_date', 
                      'last_maintenance_date', 'next_maintenance_date')
        }),
        ('المعلومات المالية', {
            'fields': ('purchase_cost', 'current_value')
        }),
        ('معلومات إضافية', {
            'fields': ('location', 'notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'needs_maintenance', 
                      'days_until_maintenance', 'age_in_years'),
            'classes': ('collapse',)
        }),
    )


@admin.register(MaintenanceType)
class MaintenanceTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'is_preventive', 'estimated_duration_hours', 'is_active']
    list_filter = ['is_preventive', 'is_active']
    search_fields = ['name', 'code']


@admin.register(MaintenanceSchedule)
class MaintenanceScheduleAdmin(admin.ModelAdmin):
    list_display = ['equipment', 'maintenance_type', 'frequency', 'next_maintenance_date', 'is_active']
    list_filter = ['frequency', 'is_active', 'equipment__production_line']
    search_fields = ['equipment__name', 'maintenance_type__name']
    readonly_fields = ['is_overdue', 'days_until_due']


@admin.register(MaintenanceRecord)
class MaintenanceRecordAdmin(admin.ModelAdmin):
    list_display = ['equipment', 'title', 'maintenance_type', 'status', 'priority', 'scheduled_date']
    list_filter = ['status', 'priority', 'maintenance_type', 'scheduled_date']
    search_fields = ['equipment__name', 'title', 'maintenance_type__name']
    readonly_fields = ['created_at', 'updated_at', 'total_cost', 'is_overdue', 'duration_variance']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('equipment', 'maintenance_type', 'schedule', 'title', 'description', 
                      'priority', 'status')
        }),
        ('التوقيت', {
            'fields': ('scheduled_date', 'actual_start_date', 'actual_end_date', 
                      'estimated_duration_hours', 'actual_duration_hours')
        }),
        ('الموظفون', {
            'fields': ('assigned_technician', 'performed_by')
        }),
        ('التكاليف', {
            'fields': ('labor_cost', 'parts_cost', 'external_cost', 'total_cost')
        }),
        ('النتائج', {
            'fields': ('work_performed', 'parts_replaced', 'findings', 'recommendations')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'is_overdue', 'duration_variance'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PerformanceMetric)
class PerformanceMetricAdmin(admin.ModelAdmin):
    list_display = ['production_line', 'equipment', 'metric_type', 'measurement_date', 
                   'target_value', 'actual_value', 'achievement_rate']
    list_filter = ['metric_type', 'measurement_date', 'production_line']
    search_fields = ['production_line__name', 'equipment__name']
    readonly_fields = ['variance', 'variance_percentage', 'achievement_rate']


@admin.register(DowntimeRecord)
class DowntimeRecordAdmin(admin.ModelAdmin):
    list_display = ['production_line', 'equipment', 'title', 'downtime_type', 'severity', 
                   'start_time', 'duration_hours', 'is_resolved']
    list_filter = ['downtime_type', 'severity', 'is_resolved', 'start_time']
    search_fields = ['production_line__name', 'equipment__name', 'title']
    readonly_fields = ['created_at', 'updated_at', 'duration_minutes', 'duration_hours', 'is_ongoing']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('production_line', 'equipment', 'title', 'downtime_type', 'severity')
        }),
        ('التوقيت', {
            'fields': ('start_time', 'end_time', 'duration_minutes', 'duration_hours')
        }),
        ('التفاصيل', {
            'fields': ('description', 'root_cause', 'corrective_action', 'preventive_action')
        }),
        ('الموظفون', {
            'fields': ('reported_by', 'resolved_by')
        }),
        ('التأثير', {
            'fields': ('production_loss', 'cost_impact')
        }),
        ('معلومات النظام', {
            'fields': ('is_resolved', 'is_ongoing', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
