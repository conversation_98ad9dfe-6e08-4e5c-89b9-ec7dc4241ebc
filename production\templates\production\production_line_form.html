{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 3rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2.5rem;
        padding: 2rem;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 20px;
        border-left: 5px solid #667eea;
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.8rem;
    }

    .section-title i {
        color: #667eea;
        font-size: 1.6rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        padding: 1rem 1.2rem;
        transition: all 0.3s ease;
        font-size: 1rem;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .form-check-input {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 8px;
        border: 2px solid #667eea;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .form-check-label {
        font-weight: 600;
        color: #2c3e50;
        margin-left: 0.8rem;
    }

    .btn-group-custom {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(102, 126, 234, 0.1);
    }

    .btn-custom {
        padding: 1rem 2.5rem;
        border-radius: 15px;
        font-weight: 700;
        font-size: 1.1rem;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        min-width: 150px;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border: 2px solid #6c757d;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .required-field::after {
        content: ' *';
        color: #e74c3c;
        font-weight: bold;
    }

    .help-text {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 0.5rem;
        font-style: italic;
    }

    .alert-custom {
        border-radius: 15px;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .alert-danger-custom {
        background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
        color: #c0392b;
        border-left: 5px solid #e74c3c;
    }

    @media (max-width: 768px) {
        .form-card {
            padding: 2rem 1.5rem;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .btn-group-custom {
            flex-direction: column;
        }
        
        .btn-custom {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-plus-circle me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'production:production_line_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- نموذج إنشاء خط الإنتاج -->
    <div class="form-card">
        {% if form.errors %}
        <div class="alert alert-danger-custom">
            <i class="bi bi-exclamation-triangle me-2"></i>
            يرجى تصحيح الأخطاء التالية:
            <ul class="mb-0 mt-2">
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- المعلومات الأساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label required-field">{{ form.name.label }}</label>
                        {{ form.name }}
                        {% if form.name.help_text %}
                        <div class="help-text">{{ form.name.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label required-field">{{ form.code.label }}</label>
                        {{ form.code }}
                        {% if form.code.help_text %}
                        <div class="help-text">{{ form.code.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-12">
                        <label class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.help_text %}
                        <div class="help-text">{{ form.description.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label required-field">{{ form.warehouse.label }}</label>
                        {{ form.warehouse }}
                        {% if form.warehouse.help_text %}
                        <div class="help-text">{{ form.warehouse.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">{{ form.supervisor.label }}</label>
                        {{ form.supervisor }}
                        {% if form.supervisor.help_text %}
                        <div class="help-text">{{ form.supervisor.help_text }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- المواصفات التقنية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-gear"></i>
                    المواصفات التقنية
                </h3>
                
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label required-field">{{ form.capacity_per_hour.label }}</label>
                        {{ form.capacity_per_hour }}
                        {% if form.capacity_per_hour.help_text %}
                        <div class="help-text">{{ form.capacity_per_hour.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label">{{ form.efficiency_rate.label }}</label>
                        {{ form.efficiency_rate }}
                        {% if form.efficiency_rate.help_text %}
                        <div class="help-text">{{ form.efficiency_rate.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label">{{ form.status.label }}</label>
                        {{ form.status }}
                        {% if form.status.help_text %}
                        <div class="help-text">{{ form.status.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">{{ form.installation_date.label }}</label>
                        {{ form.installation_date }}
                        {% if form.installation_date.help_text %}
                        <div class="help-text">{{ form.installation_date.help_text }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- المعلومات المالية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-currency-dollar"></i>
                    المعلومات المالية
                </h3>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">{{ form.setup_cost.label }}</label>
                        {{ form.setup_cost }}
                        {% if form.setup_cost.help_text %}
                        <div class="help-text">{{ form.setup_cost.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">{{ form.operating_cost_per_hour.label }}</label>
                        {{ form.operating_cost_per_hour }}
                        {% if form.operating_cost_per_hour.help_text %}
                        <div class="help-text">{{ form.operating_cost_per_hour.help_text }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- ملاحظات وإعدادات -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-chat-text"></i>
                    ملاحظات وإعدادات
                </h3>
                
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.help_text %}
                        <div class="help-text">{{ form.notes.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-12">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.help_text %}
                        <div class="help-text">{{ form.is_active.help_text }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="btn-group-custom">
                <button type="submit" class="btn btn-primary-custom">
                    <i class="bi bi-check-circle me-2"></i>
                    {% if line %}تحديث خط الإنتاج{% else %}إنشاء خط الإنتاج{% endif %}
                </button>
                <a href="{% url 'production:production_line_list' %}" class="btn btn-secondary-custom">
                    <i class="bi bi-x-circle me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
