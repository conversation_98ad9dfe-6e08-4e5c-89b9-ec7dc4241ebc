import os
import django
from decimal import Decimal

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from definitions.models import (
    ProductDefinition, ProductCategory, WarehouseDefinition, 
    UnitDefinition, CurrencyDefinition
)
from manufacturing.models import ManufacturingOrder, ManufacturingOrderRawMaterial
from django.contrib.auth.models import User

def create_sample_data():
    print("إنشاء البيانات التجريبية للتصنيع...")

    # الحصول على المستخدم admin
    admin_user = User.objects.get(username='admin')

    # إنشاء عملة إذا لم تكن موجودة
    currency, created = CurrencyDefinition.objects.get_or_create(
        code='EGP',
        defaults={
            'name': 'الجنيه المصري',
            'symbol': 'ج.م',
            'exchange_rate': Decimal('1.00'),
            'is_active': True,
            'created_by': admin_user
        }
    )
    if created:
        print("تم إنشاء عملة الجنيه المصري")
    
    # إنشاء وحدات قياس
    units_data = [
        {'code': 'KG', 'name': 'كيلوجرام', 'symbol': 'كجم'},
        {'code': 'PCS', 'name': 'قطعة', 'symbol': 'قطعة'},
        {'code': 'LTR', 'name': 'لتر', 'symbol': 'لتر'},
        {'code': 'MTR', 'name': 'متر', 'symbol': 'م'},
    ]
    
    for unit_data in units_data:
        unit_data['created_by'] = admin_user
        unit, created = UnitDefinition.objects.get_or_create(
            code=unit_data['code'],
            defaults=unit_data
        )
        if created:
            print(f"تم إنشاء وحدة القياس: {unit.name}")
    
    # إنشاء فئات المنتجات
    categories_data = [
        {'code': 'RAW', 'name': 'مواد خام'},
        {'code': 'FIN', 'name': 'منتجات تامة'},
        {'code': 'SEM', 'name': 'منتجات نصف مصنعة'},
    ]
    
    for cat_data in categories_data:
        cat_data['created_by'] = admin_user
        category, created = ProductCategory.objects.get_or_create(
            code=cat_data['code'],
            defaults=cat_data
        )
        if created:
            print(f"تم إنشاء فئة المنتج: {category.name}")
    
    # إنشاء مخازن
    warehouses_data = [
        {'code': 'RAW001', 'name': 'مخزن المواد الخام الرئيسي'},
        {'code': 'FIN001', 'name': 'مخزن المنتجات التامة'},
        {'code': 'WIP001', 'name': 'مخزن الإنتاج تحت التشغيل'},
    ]
    
    for wh_data in warehouses_data:
        wh_data['created_by'] = admin_user
        warehouse, created = WarehouseDefinition.objects.get_or_create(
            code=wh_data['code'],
            defaults=wh_data
        )
        if created:
            print(f"تم إنشاء المخزن: {warehouse.name}")
    
    # إنشاء منتجات
    raw_category = ProductCategory.objects.get(code='RAW')
    finished_category = ProductCategory.objects.get(code='FIN')
    kg_unit = UnitDefinition.objects.get(code='KG')
    pcs_unit = UnitDefinition.objects.get(code='PCS')
    
    # مواد خام
    raw_materials = [
        {'code': 'STEEL001', 'name': 'حديد خام', 'cost_price': Decimal('50.00')},
        {'code': 'PLASTIC001', 'name': 'بلاستيك خام', 'cost_price': Decimal('30.00')},
        {'code': 'RUBBER001', 'name': 'مطاط خام', 'cost_price': Decimal('25.00')},
        {'code': 'PAINT001', 'name': 'دهان', 'cost_price': Decimal('40.00')},
    ]
    
    for material_data in raw_materials:
        product, created = ProductDefinition.objects.get_or_create(
            code=material_data['code'],
            defaults={
                'name': material_data['name'],
                'category': raw_category,
                'unit': kg_unit,
                'cost_price': material_data['cost_price'],
                'selling_price': material_data['cost_price'] * Decimal('1.2'),
                'is_active': True,
                'created_by': admin_user
            }
        )
        if created:
            print(f"تم إنشاء المادة الخام: {product.name}")
    
    # منتجات تامة
    finished_products = [
        {'code': 'CHAIR001', 'name': 'كرسي مكتبي', 'cost_price': Decimal('200.00')},
        {'code': 'TABLE001', 'name': 'طاولة مكتبية', 'cost_price': Decimal('500.00')},
        {'code': 'CABINET001', 'name': 'خزانة ملفات', 'cost_price': Decimal('800.00')},
    ]
    
    for product_data in finished_products:
        product, created = ProductDefinition.objects.get_or_create(
            code=product_data['code'],
            defaults={
                'name': product_data['name'],
                'category': finished_category,
                'unit': pcs_unit,
                'cost_price': product_data['cost_price'],
                'selling_price': product_data['cost_price'] * Decimal('1.5'),
                'is_active': True,
                'created_by': admin_user
            }
        )
        if created:
            print(f"تم إنشاء المنتج التام: {product.name}")
    
    print("تم إنشاء جميع البيانات التجريبية بنجاح!")

if __name__ == '__main__':
    create_sample_data()
