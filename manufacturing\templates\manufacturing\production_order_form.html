{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    /* Manufacturing Form Styles */
    .manufacturing-form-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .form-header h2 {
        margin-bottom: 0.5rem;
        font-weight: 700;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e9ecef;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #667eea;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-left: 0.5rem;
        color: #667eea;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 1px solid #e0e6ed;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-manufacturing {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-manufacturing:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-secondary-manufacturing {
        background: #6c757d;
        border: none;
        color: white;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-secondary-manufacturing:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
    }

    /* Materials Section */
    .materials-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e9ecef;
    }

    .material-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e9ecef;
    }

    .add-material-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        border-radius: 10px;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .add-material-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .remove-material-btn {
        background: #dc3545;
        border: none;
        color: white;
        border-radius: 8px;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    /* Cost Calculator */
    .cost-calculator {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 2rem;
        border: 1px solid #dee2e6;
    }

    .cost-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
    }

    .cost-item:last-child {
        border-bottom: none;
        font-weight: 600;
        font-size: 1.1rem;
        color: #495057;
    }

    .cost-value {
        font-weight: 600;
        color: #667eea;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .manufacturing-form-container {
            padding: 1rem;
        }
        
        .form-header {
            padding: 1.5rem;
        }
        
        .form-section {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Form Header -->
    <div class="form-header">
        <h2>{{ title }}</h2>
        <p>إنشاء أمر إنتاج جديد مع حساب التكاليف التلقائي</p>
    </div>

    <form method="post" id="productionOrderForm">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="manufacturing-form-container">
            <div class="form-section">
                <h4 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    المعلومات الأساسية
                </h4>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">{{ form.product.label }}</label>
                        {{ form.product }}
                        {% if form.product.errors %}
                            <div class="text-danger small">{{ form.product.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.quantity.label }}</label>
                        {{ form.quantity }}
                        {% if form.quantity.errors %}
                            <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.target_warehouse.label }}</label>
                        {{ form.target_warehouse }}
                        {% if form.target_warehouse.errors %}
                            <div class="text-danger small">{{ form.target_warehouse.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.priority.label }}</label>
                        {{ form.priority }}
                        {% if form.priority.errors %}
                            <div class="text-danger small">{{ form.priority.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.planned_start_date.label }}</label>
                        {{ form.planned_start_date }}
                        {% if form.planned_start_date.errors %}
                            <div class="text-danger small">{{ form.planned_start_date.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.planned_end_date.label }}</label>
                        {{ form.planned_end_date }}
                        {% if form.planned_end_date.errors %}
                            <div class="text-danger small">{{ form.planned_end_date.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-12">
                        <label class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Materials Section -->
        <div class="manufacturing-form-container">
            <div class="materials-section">
                <h4 class="section-title">
                    <i class="bi bi-box-seam"></i>
                    المواد الخام المطلوبة
                </h4>
                <div id="materials-container">
                    <div class="material-item">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">المادة الخام</label>
                                <select class="form-select material-select" name="material" required>
                                    <option value="">اختر المادة الخام</option>
                                    {% for product in raw_materials %}
                                        <option value="{{ product.id }}">{{ product.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">المخزن</label>
                                <select class="form-select warehouse-select" name="warehouse" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الكمية المطلوبة</label>
                                <input type="number" class="form-control quantity-input" name="required_quantity" step="0.01" min="0.01" required>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">تكلفة الوحدة</label>
                                <input type="number" class="form-control unit-cost-input" name="unit_cost" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الإجمالي</label>
                                <input type="text" class="form-control total-cost-display" readonly>
                                <button type="button" class="btn remove-material-btn mt-2" style="display: none;">حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn add-material-btn" id="addMaterialBtn">
                    <i class="bi bi-plus-circle me-2"></i>إضافة مادة خام
                </button>
            </div>
        </div>

        <!-- Cost Calculator -->
        <div class="manufacturing-form-container">
            <div class="cost-calculator">
                <h4 class="section-title">
                    <i class="bi bi-calculator"></i>
                    حاسبة التكلفة
                </h4>
                <div class="cost-item">
                    <span>تكلفة المواد الخام:</span>
                    <span class="cost-value" id="materialsCost">0.00 ج.م</span>
                </div>
                <div class="cost-item">
                    <span>تكلفة العمالة (تقديرية):</span>
                    <span class="cost-value" id="laborCost">0.00 ج.م</span>
                </div>
                <div class="cost-item">
                    <span>التكاليف الإضافية (15%):</span>
                    <span class="cost-value" id="overheadCost">0.00 ج.م</span>
                </div>
                <div class="cost-item">
                    <span>إجمالي التكلفة:</span>
                    <span class="cost-value" id="totalCost">0.00 ج.م</span>
                </div>
                <div class="cost-item">
                    <span>تكلفة الوحدة:</span>
                    <span class="cost-value" id="unitCost">0.00 ج.م</span>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="manufacturing-form-container">
            <div class="d-flex justify-content-between">
                <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-secondary-manufacturing">
                    <i class="bi bi-arrow-right me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-manufacturing">
                    <i class="bi bi-check-circle me-2"></i>إنشاء أمر الإنتاج
                </button>
            </div>
        </div>
    </form>
</div>

<script>
// Manufacturing Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    let materialCount = 1;
    
    // Add material functionality
    document.getElementById('addMaterialBtn').addEventListener('click', function() {
        const container = document.getElementById('materials-container');
        const newMaterial = container.children[0].cloneNode(true);
        
        // Clear values
        newMaterial.querySelectorAll('input, select').forEach(input => {
            input.value = '';
        });
        
        // Show remove button
        const removeBtn = newMaterial.querySelector('.remove-material-btn');
        removeBtn.style.display = 'block';
        removeBtn.addEventListener('click', function() {
            newMaterial.remove();
            calculateTotalCost();
        });
        
        container.appendChild(newMaterial);
        materialCount++;
    });
    
    // Calculate costs when inputs change
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('quantity-input') || e.target.classList.contains('unit-cost-input')) {
            calculateRowTotal(e.target.closest('.material-item'));
            calculateTotalCost();
        }
        
        if (e.target.name === 'quantity') {
            calculateTotalCost();
        }
    });
    
    // Calculate row total
    function calculateRowTotal(row) {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const unitCost = parseFloat(row.querySelector('.unit-cost-input').value) || 0;
        const total = quantity * unitCost;
        
        row.querySelector('.total-cost-display').value = total.toFixed(2) + ' ج.م';
    }
    
    // Calculate total cost
    function calculateTotalCost() {
        let materialsCost = 0;
        
        document.querySelectorAll('.material-item').forEach(row => {
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const unitCost = parseFloat(row.querySelector('.unit-cost-input').value) || 0;
            materialsCost += quantity * unitCost;
        });
        
        const laborCost = materialsCost * 0.2; // 20% تقديرية
        const overheadCost = (materialsCost + laborCost) * 0.15; // 15%
        const totalCost = materialsCost + laborCost + overheadCost;
        
        const productionQuantity = parseFloat(document.querySelector('[name="quantity"]').value) || 1;
        const unitCost = totalCost / productionQuantity;
        
        document.getElementById('materialsCost').textContent = materialsCost.toFixed(2) + ' ج.م';
        document.getElementById('laborCost').textContent = laborCost.toFixed(2) + ' ج.م';
        document.getElementById('overheadCost').textContent = overheadCost.toFixed(2) + ' ج.م';
        document.getElementById('totalCost').textContent = totalCost.toFixed(2) + ' ج.م';
        document.getElementById('unitCost').textContent = unitCost.toFixed(2) + ' ج.م';
    }
    
    // Set current datetime
    const now = new Date();
    const currentDateTime = now.toISOString().slice(0, 16);
    document.querySelectorAll('input[type="datetime-local"]').forEach(input => {
        if (!input.value) {
            input.value = currentDateTime;
        }
    });
});
</script>
{% endblock %}
