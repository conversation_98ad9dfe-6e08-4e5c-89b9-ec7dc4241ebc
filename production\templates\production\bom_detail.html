{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <h1 class="page-title">
            <i class="bi bi-list-check me-3"></i>
            {{ bom.name }}
        </h1>
    </div>

    <div class="content-card">
        <h3>معلومات قائمة المواد</h3>
        <div class="row g-3">
            <div class="col-md-6">
                <strong>الكود:</strong> {{ bom.code }}
            </div>
            <div class="col-md-6">
                <strong>المنتج:</strong> {{ bom.product.name }}
            </div>
            <div class="col-md-6">
                <strong>الإصدار:</strong> {{ bom.version }}
            </div>
            <div class="col-md-6">
                <strong>الحالة:</strong> {{ bom.get_status_display }}
            </div>
            <div class="col-md-6">
                <strong>الكمية المنتجة:</strong> {{ bom.quantity_produced }}
            </div>
            <div class="col-md-6">
                <strong>وقت الإنتاج:</strong> {{ bom.production_time_minutes }} دقيقة
            </div>
            <div class="col-md-6">
                <strong>تكلفة العمالة:</strong> {{ bom.labor_cost }} جنيه
            </div>
            <div class="col-md-6">
                <strong>التكاليف الإضافية:</strong> {{ bom.overhead_cost }} جنيه
            </div>
            <div class="col-md-6">
                <strong>إجمالي تكلفة المواد:</strong> {{ total_material_cost|floatformat:2 }} جنيه
            </div>
            <div class="col-md-6">
                <strong>التكلفة الإجمالية:</strong> {{ bom.total_cost|floatformat:2 }} جنيه
            </div>
        </div>
    </div>

    <div class="content-card">
        <h3>عناصر قائمة المواد ({{ total_items }})</h3>
        {% if items %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>التسلسل</th>
                        <th>المادة</th>
                        <th>النوع</th>
                        <th>الكمية المطلوبة</th>
                        <th>تكلفة الوحدة</th>
                        <th>نسبة الفاقد</th>
                        <th>التكلفة الإجمالية</th>
                        <th>حرجة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>{{ item.sequence }}</td>
                        <td>{{ item.material.name }}</td>
                        <td>{{ item.get_item_type_display }}</td>
                        <td>{{ item.quantity_required }}</td>
                        <td>{{ item.unit_cost }} جنيه</td>
                        <td>{{ item.waste_percentage }}%</td>
                        <td>{{ item.total_cost|floatformat:2 }} جنيه</td>
                        <td>
                            {% if item.is_critical %}
                                <span class="badge bg-danger">نعم</span>
                            {% else %}
                                <span class="badge bg-secondary">لا</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-muted">لا توجد عناصر في قائمة المواد</p>
        {% endif %}
    </div>

    <div class="text-center">
        <a href="{% url 'production:bom_update' bom.pk %}" class="btn btn-primary me-3">
            <i class="bi bi-pencil me-2"></i>تعديل
        </a>
        <a href="{% url 'production:bom_copy' bom.pk %}" class="btn btn-info me-3">
            <i class="bi bi-files me-2"></i>نسخ
        </a>
        <a href="{% url 'production:bom_list' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}
