from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg
from django.utils import timezone
from datetime import timedelta
from .models import (
    ProductionLine, BillOfMaterials, BOMItem, ProductionOrder,
    ProductionOrderItem, QualityCheck, ProductionReport
)
from .forms import (
    ProductionLineForm, BillOfMaterialsForm, BOMItemForm, ProductionOrderForm,
    QualityCheckForm, ProductionReportForm, BOMItemFormSet, ManufacturingSearchForm
)


@login_required
def manufacturing_dashboard(request):
    """لوحة تحكم التصنيع المتقدمة"""

    # إحصائيات عامة
    total_production_lines = ProductionLine.objects.filter(is_active=True).count()
    active_production_lines = ProductionLine.objects.filter(status='active').count()
    total_boms = BillOfMaterials.objects.filter(is_active=True).count()
    active_boms = BillOfMaterials.objects.filter(status='active').count()

    # أوامر الإنتاج
    total_orders = ProductionOrder.objects.count()
    orders_in_progress = ProductionOrder.objects.filter(status='in_progress').count()
    orders_completed_today = ProductionOrder.objects.filter(
        status='completed',
        actual_end_date__date=timezone.now().date()
    ).count()
    orders_overdue = ProductionOrder.objects.filter(
        planned_end_date__lt=timezone.now(),
        status__in=['planned', 'released', 'in_progress']
    ).count()

    # إحصائيات الإنتاج
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)

    production_this_week = ProductionReport.objects.filter(
        report_date__gte=week_ago
    ).aggregate(
        total_produced=Sum('quantity_produced'),
        total_scrapped=Sum('quantity_scrapped'),
        avg_efficiency=Avg('efficiency_rate')
    )

    # فحوصات الجودة
    quality_checks_today = QualityCheck.objects.filter(
        check_date__date=today
    ).count()

    quality_pass_rate = QualityCheck.objects.filter(
        check_date__gte=week_ago,
        status='passed'
    ).aggregate(
        total_checked=Sum('quantity_checked'),
        total_passed=Sum('quantity_passed')
    )

    # حساب معدل النجاح
    pass_rate = 0
    if quality_pass_rate['total_checked'] and quality_pass_rate['total_checked'] > 0:
        pass_rate = (quality_pass_rate['total_passed'] / quality_pass_rate['total_checked']) * 100

    # أحدث أوامر الإنتاج
    recent_orders = ProductionOrder.objects.select_related(
        'bom__product', 'production_line'
    ).order_by('-created_at')[:10]

    # خطوط الإنتاج النشطة
    production_lines = ProductionLine.objects.filter(
        is_active=True
    ).select_related('warehouse', 'supervisor')[:8]

    # التنبيهات
    maintenance_alerts = ProductionLine.objects.filter(
        needs_maintenance=True,
        is_active=True
    ).count()

    context = {
        # إحصائيات عامة
        'total_production_lines': total_production_lines,
        'active_production_lines': active_production_lines,
        'total_boms': total_boms,
        'active_boms': active_boms,

        # أوامر الإنتاج
        'total_orders': total_orders,
        'orders_in_progress': orders_in_progress,
        'orders_completed_today': orders_completed_today,
        'orders_overdue': orders_overdue,

        # إحصائيات الإنتاج
        'production_this_week': production_this_week,
        'quality_checks_today': quality_checks_today,
        'quality_pass_rate': round(pass_rate, 1),

        # البيانات
        'recent_orders': recent_orders,
        'production_lines': production_lines,

        # التنبيهات
        'maintenance_alerts': maintenance_alerts,

        # معلومات الصفحة
        'page_title': 'لوحة تحكم التصنيع',
        'page_title_en': 'Manufacturing Dashboard',
    }

    return render(request, 'manufacturing/dashboard.html', context)

# ==================== خطوط الإنتاج ====================

@login_required
def production_line_list(request):
    """قائمة خطوط الإنتاج"""
    search_form = ManufacturingSearchForm(request.GET)
    production_lines = ProductionLine.objects.select_related('warehouse', 'supervisor')

    # البحث والفلترة
    if search_form.is_valid():
        search = search_form.cleaned_data.get('search')
        status = search_form.cleaned_data.get('status')

        if search:
            production_lines = production_lines.filter(
                Q(name__icontains=search) | Q(code__icontains=search)
            )

        if status:
            production_lines = production_lines.filter(status=status)

    # الترقيم
    paginator = Paginator(production_lines, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'page_title': 'خطوط الإنتاج',
    }
    return render(request, 'manufacturing/production_line_list.html', context)


@login_required
def production_line_create(request):
    """إنشاء خط إنتاج جديد"""
    if request.method == 'POST':
        form = ProductionLineForm(request.POST)
        if form.is_valid():
            production_line = form.save(commit=False)
            production_line.created_by = request.user
            production_line.save()
            messages.success(request, 'تم إنشاء خط الإنتاج بنجاح')
            return redirect('manufacturing:production_line_list')
    else:
        form = ProductionLineForm()

    context = {
        'form': form,
        'page_title': 'إنشاء خط إنتاج جديد',
    }
    return render(request, 'manufacturing/production_line_form.html', context)


@login_required
def production_line_detail(request, pk):
    """تفاصيل خط الإنتاج"""
    production_line = get_object_or_404(ProductionLine, pk=pk)

    # إحصائيات خط الإنتاج
    orders_count = ProductionOrder.objects.filter(production_line=production_line).count()
    orders_in_progress = ProductionOrder.objects.filter(
        production_line=production_line,
        status='in_progress'
    ).count()

    # أحدث أوامر الإنتاج
    recent_orders = ProductionOrder.objects.filter(
        production_line=production_line
    ).select_related('bom__product').order_by('-created_at')[:10]

    context = {
        'production_line': production_line,
        'orders_count': orders_count,
        'orders_in_progress': orders_in_progress,
        'recent_orders': recent_orders,
        'page_title': f'تفاصيل خط الإنتاج - {production_line.name}',
    }
    return render(request, 'manufacturing/production_line_detail.html', context)


# ==================== قوائم المواد ====================

@login_required
def bom_list(request):
    """قائمة قوائم المواد"""
    search_form = ManufacturingSearchForm(request.GET)
    boms = BillOfMaterials.objects.select_related('product', 'created_by')

    # البحث والفلترة
    if search_form.is_valid():
        search = search_form.cleaned_data.get('search')
        status = search_form.cleaned_data.get('status')

        if search:
            boms = boms.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(product__name__icontains=search)
            )

        if status:
            boms = boms.filter(status=status)

    # الترقيم
    paginator = Paginator(boms, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'page_title': 'قوائم المواد',
    }
    return render(request, 'manufacturing/bom_list.html', context)


@login_required
def bom_create(request):
    """إنشاء قائمة مواد جديدة"""
    if request.method == 'POST':
        form = BillOfMaterialsForm(request.POST)
        formset = BOMItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            bom = form.save(commit=False)
            bom.created_by = request.user
            bom.save()

            formset.instance = bom
            formset.save()

            messages.success(request, 'تم إنشاء قائمة المواد بنجاح')
            return redirect('manufacturing:bom_detail', pk=bom.pk)
    else:
        form = BillOfMaterialsForm()
        formset = BOMItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'page_title': 'إنشاء قائمة مواد جديدة',
    }
    return render(request, 'manufacturing/bom_form.html', context)


@login_required
def bom_detail(request, pk):
    """تفاصيل قائمة المواد"""
    bom = get_object_or_404(BillOfMaterials, pk=pk)
    bom_items = bom.bom_items.select_related('material').order_by('sequence')

    # إحصائيات قائمة المواد
    orders_count = ProductionOrder.objects.filter(bom=bom).count()

    context = {
        'bom': bom,
        'bom_items': bom_items,
        'orders_count': orders_count,
        'page_title': f'تفاصيل قائمة المواد - {bom.name}',
    }
    return render(request, 'manufacturing/bom_detail.html', context)

# ==================== أوامر الإنتاج ====================

@login_required
def production_order_list(request):
    """قائمة أوامر الإنتاج"""
    search_form = ManufacturingSearchForm(request.GET)
    orders = ProductionOrder.objects.select_related(
        'bom__product', 'production_line', 'created_by'
    )

    # البحث والفلترة
    if search_form.is_valid():
        search = search_form.cleaned_data.get('search')
        status = search_form.cleaned_data.get('status')
        date_from = search_form.cleaned_data.get('date_from')
        date_to = search_form.cleaned_data.get('date_to')

        if search:
            orders = orders.filter(
                Q(order_number__icontains=search) |
                Q(bom__product__name__icontains=search) |
                Q(customer_order_ref__icontains=search)
            )

        if status:
            orders = orders.filter(status=status)

        if date_from:
            orders = orders.filter(planned_start_date__date__gte=date_from)

        if date_to:
            orders = orders.filter(planned_end_date__date__lte=date_to)

    # الترقيم
    paginator = Paginator(orders, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'page_title': 'أوامر الإنتاج',
    }
    return render(request, 'manufacturing/production_order_list.html', context)


@login_required
def production_order_create(request):
    """إنشاء أمر إنتاج جديد"""
    if request.method == 'POST':
        form = ProductionOrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            order.save()

            # إنشاء عناصر أمر الإنتاج من قائمة المواد
            for bom_item in order.bom.bom_items.all():
                quantity_required = bom_item.quantity_with_waste * order.quantity_ordered
                ProductionOrderItem.objects.create(
                    production_order=order,
                    bom_item=bom_item,
                    quantity_required=quantity_required,
                    warehouse=order.production_line.warehouse
                )

            messages.success(request, 'تم إنشاء أمر الإنتاج بنجاح')
            return redirect('manufacturing:production_order_detail', pk=order.pk)
    else:
        form = ProductionOrderForm()

    context = {
        'form': form,
        'page_title': 'إنشاء أمر إنتاج جديد',
    }
    return render(request, 'manufacturing/production_order_form.html', context)


@login_required
def production_order_detail(request, pk):
    """تفاصيل أمر الإنتاج"""
    order = get_object_or_404(ProductionOrder, pk=pk)
    order_items = order.order_items.select_related('bom_item__material').order_by('bom_item__sequence')
    quality_checks = order.quality_checks.order_by('-check_date')
    production_reports = order.production_reports.order_by('-report_date', '-shift')

    context = {
        'order': order,
        'order_items': order_items,
        'quality_checks': quality_checks,
        'production_reports': production_reports,
        'page_title': f'تفاصيل أمر الإنتاج - {order.order_number}',
    }
    return render(request, 'manufacturing/production_order_detail.html', context)


# ==================== فحوصات الجودة ====================

@login_required
def quality_check_create(request, order_pk):
    """إنشاء فحص جودة لأمر إنتاج"""
    order = get_object_or_404(ProductionOrder, pk=order_pk)

    if request.method == 'POST':
        form = QualityCheckForm(request.POST)
        if form.is_valid():
            quality_check = form.save(commit=False)
            quality_check.production_order = order
            quality_check.save()

            messages.success(request, 'تم إنشاء فحص الجودة بنجاح')
            return redirect('manufacturing:production_order_detail', pk=order.pk)
    else:
        form = QualityCheckForm()

    context = {
        'form': form,
        'order': order,
        'page_title': f'فحص جودة - {order.order_number}',
    }
    return render(request, 'manufacturing/quality_check_form.html', context)


# ==================== تقارير الإنتاج ====================

@login_required
def production_report_create(request, order_pk):
    """إنشاء تقرير إنتاج"""
    order = get_object_or_404(ProductionOrder, pk=order_pk)

    if request.method == 'POST':
        form = ProductionReportForm(request.POST)
        if form.is_valid():
            report = form.save(commit=False)
            report.production_order = order
            report.created_by = request.user
            report.save()

            # تحديث كمية الإنتاج في الأمر
            order.quantity_produced += report.quantity_produced
            order.quantity_scrapped += report.quantity_scrapped
            order.save()

            messages.success(request, 'تم إنشاء تقرير الإنتاج بنجاح')
            return redirect('manufacturing:production_order_detail', pk=order.pk)
    else:
        form = ProductionReportForm()

    context = {
        'form': form,
        'order': order,
        'page_title': f'تقرير إنتاج - {order.order_number}',
    }
    return render(request, 'manufacturing/production_report_form.html', context)


# ==================== APIs ====================

@login_required
def get_bom_info(request):
    """API للحصول على معلومات قائمة المواد"""
    bom_id = request.GET.get('bom_id')
    if not bom_id:
        return JsonResponse({'error': 'BOM ID is required'}, status=400)

    try:
        bom = BillOfMaterials.objects.get(id=bom_id)
        data = {
            'id': bom.id,
            'name': bom.name,
            'product_name': bom.product.name,
            'quantity_produced': float(bom.quantity_produced),
            'production_time_minutes': bom.production_time_minutes,
            'total_cost': float(bom.total_cost),
            'cost_per_unit': float(bom.cost_per_unit),
        }
        return JsonResponse(data)
    except BillOfMaterials.DoesNotExist:
        return JsonResponse({'error': 'BOM not found'}, status=404)


@login_required
def calculate_production_cost(request):
    """API لحساب تكلفة الإنتاج"""
    bom_id = request.GET.get('bom_id')
    quantity = request.GET.get('quantity', 1)

    try:
        bom = BillOfMaterials.objects.get(id=bom_id)
        quantity = float(quantity)

        total_cost = bom.cost_per_unit * quantity

        data = {
            'bom_id': bom.id,
            'quantity': quantity,
            'cost_per_unit': float(bom.cost_per_unit),
            'total_cost': float(total_cost),
            'material_cost': float(bom.total_material_cost * quantity / bom.quantity_produced),
            'labor_cost': float(bom.labor_cost * quantity / bom.quantity_produced),
            'overhead_cost': float(bom.overhead_cost * quantity / bom.quantity_produced),
        }
        return JsonResponse(data)
    except (BillOfMaterials.DoesNotExist, ValueError):
        return JsonResponse({'error': 'Invalid data'}, status=400)
