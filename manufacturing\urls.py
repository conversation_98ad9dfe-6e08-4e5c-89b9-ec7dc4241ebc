from django.urls import path
from . import views

app_name = 'manufacturing'

urlpatterns = [
    # لوحة التحكم
    path('', views.manufacturing_dashboard, name='dashboard'),

    # خطوط الإنتاج
    path('production-lines/', views.production_line_list, name='production_line_list'),
    path('production-lines/create/', views.production_line_create, name='production_line_create'),
    path('production-lines/<int:pk>/', views.production_line_detail, name='production_line_detail'),

    # قوائم المواد
    path('bom/', views.bom_list, name='bom_list'),
    path('bom/create/', views.bom_create, name='bom_create'),
    path('bom/<int:pk>/', views.bom_detail, name='bom_detail'),

    # أوامر الإنتاج
    path('orders/', views.production_order_list, name='production_order_list'),
    path('orders/create/', views.production_order_create, name='production_order_create'),
    path('orders/<int:pk>/', views.production_order_detail, name='production_order_detail'),

    # فحوصات الجودة
    path('orders/<int:order_pk>/quality-check/', views.quality_check_create, name='quality_check_create'),

    # تقارير الإنتاج
    path('orders/<int:order_pk>/production-report/', views.production_report_create, name='production_report_create'),

    # AJAX APIs
    path('api/bom-info/', views.get_bom_info, name='get_bom_info'),
    path('api/calculate-cost/', views.calculate_production_cost, name='calculate_production_cost'),

    # Equipment URLs
    path('equipment/', views.equipment_list, name='equipment_list'),
    path('equipment/<int:pk>/', views.equipment_detail, name='equipment_detail'),
    path('equipment/create/', views.equipment_create, name='equipment_create'),
    path('equipment/<int:pk>/update/', views.equipment_update, name='equipment_update'),

    # Maintenance URLs
    path('maintenance/', views.maintenance_dashboard, name='maintenance_dashboard'),
    path('maintenance/schedules/', views.maintenance_schedule_list, name='maintenance_schedule_list'),
    path('maintenance/records/', views.maintenance_record_list, name='maintenance_record_list'),
    path('maintenance/records/create/', views.maintenance_record_create, name='maintenance_record_create'),

    # Performance URLs
    path('performance/', views.performance_dashboard, name='performance_dashboard'),
    path('performance/metrics/create/', views.performance_metric_create, name='performance_metric_create'),
    path('performance/report/', views.performance_report, name='performance_report'),

    # Downtime URLs
    path('downtime/', views.downtime_record_list, name='downtime_record_list'),
    path('downtime/create/', views.downtime_record_create, name='downtime_record_create'),
]
