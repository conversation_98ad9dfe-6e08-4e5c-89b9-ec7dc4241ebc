from django.urls import path
from . import views

app_name = 'manufacturing'

urlpatterns = [
    # لوحة التحكم
    path('', views.manufacturing_dashboard, name='dashboard'),

    # خطوط الإنتاج
    path('production-lines/', views.production_line_list, name='production_line_list'),
    path('production-lines/create/', views.production_line_create, name='production_line_create'),
    path('production-lines/<int:pk>/', views.production_line_detail, name='production_line_detail'),

    # قوائم المواد
    path('bom/', views.bom_list, name='bom_list'),
    path('bom/create/', views.bom_create, name='bom_create'),
    path('bom/<int:pk>/', views.bom_detail, name='bom_detail'),

    # أوامر الإنتاج
    path('orders/', views.production_order_list, name='production_order_list'),
    path('orders/create/', views.production_order_create, name='production_order_create'),
    path('orders/<int:pk>/', views.production_order_detail, name='production_order_detail'),

    # فحوصات الجودة
    path('orders/<int:order_pk>/quality-check/', views.quality_check_create, name='quality_check_create'),

    # تقارير الإنتاج
    path('orders/<int:order_pk>/production-report/', views.production_report_create, name='production_report_create'),

    # AJAX APIs
    path('api/bom-info/', views.get_bom_info, name='get_bom_info'),
    path('api/calculate-cost/', views.calculate_production_cost, name='calculate_production_cost'),
]
