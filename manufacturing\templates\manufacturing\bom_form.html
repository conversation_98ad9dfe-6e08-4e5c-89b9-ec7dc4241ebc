{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin: 2rem 0;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #667eea;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .alert-custom {
        border: none;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-info-custom {
        background: rgba(13, 202, 240, 0.1);
        color: #0dcaf0;
        border-left: 4px solid #0dcaf0;
    }

    .form-text-custom {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .formset-section {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 15px;
        padding: 2rem;
        margin-top: 2rem;
        border: 2px solid rgba(102, 126, 234, 0.1);
    }

    .formset-item {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    @media (max-width: 768px) {
        .form-container {
            padding: 2rem 1.5rem;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .btn-group-custom {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-check me-3"></i>
                    إنشاء قائمة مواد جديدة
                </h1>
                <p class="text-white-50 mb-0 mt-2">إنشاء قائمة مواد (BOM) لمنتج جديد</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:bom_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- النموذج -->
    <div class="form-container">
        <div class="alert alert-info-custom">
            <i class="bi bi-info-circle me-2"></i>
            <strong>ملاحظة:</strong> قائمة المواد تحدد جميع المواد والمكونات المطلوبة لإنتاج المنتج النهائي.
        </div>

        <form method="post" id="bomForm">
            {% csrf_token %}
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">اسم قائمة المواد</label>
                            {{ form.name }}
                            <div class="form-text-custom">اسم وصفي لقائمة المواد</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">كود قائمة المواد</label>
                            {{ form.code }}
                            <div class="form-text-custom">كود فريد لتمييز قائمة المواد</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">المنتج النهائي</label>
                            {{ form.product }}
                            <div class="form-text-custom">المنتج الذي ستنتجه هذه القائمة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">الإصدار</label>
                            {{ form.version }}
                            <div class="form-text-custom">رقم إصدار قائمة المواد</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الإنتاج -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-gear"></i>
                    تفاصيل الإنتاج
                </h3>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label required-field">الكمية المنتجة</label>
                            {{ form.quantity_produced }}
                            <div class="form-text-custom">عدد الوحدات المنتجة من هذه القائمة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label required-field">وقت الإنتاج (دقيقة)</label>
                            {{ form.production_time_minutes }}
                            <div class="form-text-custom">الوقت المطلوب للإنتاج بالدقائق</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label required-field">الحالة</label>
                            {{ form.status }}
                            <div class="form-text-custom">حالة قائمة المواد</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تكلفة العمالة</label>
                            {{ form.labor_cost }}
                            <div class="form-text-custom">تكلفة العمالة المباشرة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">التكاليف الإضافية</label>
                            {{ form.overhead_cost }}
                            <div class="form-text-custom">التكاليف الإضافية والعامة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label required-field">تاريخ السريان</label>
                            {{ form.effective_date }}
                            <div class="form-text-custom">تاريخ بدء سريان هذه القائمة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-file-text"></i>
                    معلومات إضافية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تاريخ الانتهاء</label>
                            {{ form.expiry_date }}
                            <div class="form-text-custom">تاريخ انتهاء صلاحية هذه القائمة (اختياري)</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    {{ form.notes }}
                    <div class="form-text-custom">أي ملاحظات أو تعليمات خاصة</div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-3 justify-content-end">
                <a href="{% url 'manufacturing:bom_list' %}" class="btn-secondary-custom">
                    <i class="bi bi-x-circle"></i>
                    إلغاء
                </a>
                <button type="submit" class="btn-primary-custom">
                    <i class="bi bi-check-circle"></i>
                    إنشاء قائمة المواد
                </button>
            </div>
        </form>

        <!-- قسم عناصر قائمة المواد -->
        <div class="formset-section">
            <h4 class="mb-3" style="color: #2c3e50;">
                <i class="bi bi-list-ul me-2"></i>
                عناصر قائمة المواد
            </h4>
            <p class="text-muted mb-3">سيتم إضافة عناصر قائمة المواد بعد إنشاء القائمة الأساسية.</p>
            
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> يمكنك إضافة المواد والمكونات المطلوبة بعد حفظ قائمة المواد الأساسية.
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.getElementById('bomForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإنشاء...';
        submitBtn.disabled = true;
    });

    // تنسيق حقول التاريخ
    const dateInputs = form.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        if (!input.value && input.name === 'effective_date') {
            const today = new Date();
            input.value = today.toISOString().split('T')[0];
        }
    });

    // إنشاء كود تلقائي بناءً على اسم المنتج
    const productSelect = form.querySelector('select[name="product"]');
    const codeInput = form.querySelector('input[name="code"]');
    
    if (productSelect && codeInput) {
        productSelect.addEventListener('change', function() {
            if (this.value && !codeInput.value) {
                const selectedOption = this.options[this.selectedIndex];
                const productName = selectedOption.text;
                const code = 'BOM-' + productName.substring(0, 3).toUpperCase() + '-001';
                codeInput.value = code;
            }
        });
    }
});
</script>
{% endblock %}
