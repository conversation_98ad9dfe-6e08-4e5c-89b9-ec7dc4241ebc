import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from django.contrib.auth.models import User

# إنشاء مستخدم admin إذا لم يكن موجوداً
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print("تم إنشاء مستخدم admin بنجاح!")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
else:
    print("مستخدم admin موجود بالفعل")
