{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .search-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .equipment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .equipment-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .equipment-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .equipment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    }

    .equipment-name {
        font-size: 1.3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .equipment-info {
        margin-bottom: 1rem;
    }

    .equipment-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;
        padding: 0.5rem;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 10px;
    }

    .equipment-info-label {
        font-weight: 600;
        color: #495057;
    }

    .equipment-info-value {
        font-weight: 500;
        color: #2c3e50;
    }

    .btn-group-custom {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .btn-custom {
        flex: 1;
        padding: 0.8rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        text-align: center;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        margin: 2rem 0;
    }

    .maintenance-alert {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: #e74c3c;
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .condition-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .condition-excellent { background: #27ae60; color: white; }
    .condition-good { background: #2ecc71; color: white; }
    .condition-fair { background: #f39c12; color: white; }
    .condition-poor { background: #e67e22; color: white; }
    .condition-critical { background: #e74c3c; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    المعدات والآلات
                </h1>
                <p class="text-white-50 mb-0 mt-2">إدارة ومتابعة جميع المعدات والآلات الصناعية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:equipment_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>معدة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label fw-bold">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="اسم المعدة أو الكود أو الشركة المصنعة..." 
                       value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for status_code, status_name in equipment_statuses %}
                    <option value="{{ status_code }}" {% if request.GET.status == status_code %}selected{% endif %}>
                        {{ status_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">خط الإنتاج</label>
                <select name="production_line" class="form-select">
                    <option value="">جميع خطوط الإنتاج</option>
                    {% for line in production_lines %}
                    <option value="{{ line.id }}" {% if request.GET.production_line == line.id|stringformat:"s" %}selected{% endif %}>
                        {{ line.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>

    <!-- قائمة المعدات -->
    {% if page_obj %}
    <div class="equipment-grid">
        {% for equipment in page_obj %}
        <div class="equipment-card">
            {% if equipment.needs_maintenance %}
            <div class="maintenance-alert">
                <i class="bi bi-exclamation-triangle me-1"></i>تحتاج صيانة
            </div>
            {% endif %}
            
            <div class="equipment-header">
                <div class="equipment-name">{{ equipment.name }}</div>
                <div>
                    {% status_badge equipment.status "equipment" %}
                </div>
            </div>

            <div class="equipment-info">
                <div class="equipment-info-item">
                    <span class="equipment-info-label">الكود:</span>
                    <span class="equipment-info-value">{{ equipment.code }}</span>
                </div>
                <div class="equipment-info-item">
                    <span class="equipment-info-label">الفئة:</span>
                    <span class="equipment-info-value">{{ equipment.category.name }}</span>
                </div>
                <div class="equipment-info-item">
                    <span class="equipment-info-label">خط الإنتاج:</span>
                    <span class="equipment-info-value">{{ equipment.production_line.name }}</span>
                </div>
                <div class="equipment-info-item">
                    <span class="equipment-info-label">الشركة المصنعة:</span>
                    <span class="equipment-info-value">{{ equipment.manufacturer|default:"غير محدد" }}</span>
                </div>
                <div class="equipment-info-item">
                    <span class="equipment-info-label">الحالة الفنية:</span>
                    <span class="equipment-info-value">
                        <span class="condition-badge condition-{{ equipment.condition }}">
                            {{ equipment.get_condition_display }}
                        </span>
                    </span>
                </div>
                {% if equipment.next_maintenance_date %}
                <div class="equipment-info-item">
                    <span class="equipment-info-label">الصيانة القادمة:</span>
                    <span class="equipment-info-value">{{ equipment.next_maintenance_date|date:"Y/m/d" }}</span>
                </div>
                {% endif %}
            </div>

            <div class="btn-group-custom">
                <a href="{% url 'manufacturing:equipment_detail' equipment.pk %}" 
                   class="btn-custom btn-primary-custom">
                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                </a>
                <a href="{% url 'manufacturing:equipment_update' equipment.pk %}" 
                   class="btn-custom btn-outline-primary">
                    <i class="bi bi-pencil me-2"></i>تعديل
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    {% else %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="bi bi-gear-wide-connected" style="font-size: 4rem; color: #667eea;"></i>
        </div>
        <h3 style="color: #2c3e50;">لا توجد معدات</h3>
        <p style="color: #6c757d;">لم يتم العثور على معدات. ابدأ بإضافة معدة جديدة.</p>
        <a href="{% url 'manufacturing:equipment_create' %}" class="btn btn-primary btn-lg">
            <i class="bi bi-plus-circle me-2"></i>إضافة معدة جديدة
        </a>
    </div>
    {% endif %}

    <!-- أزرار العودة -->
    <div class="text-center mt-4">
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                <i class="bi bi-house me-2"></i>لوحة التحكم
            </a>
            <a href="{% url 'manufacturing:maintenance_dashboard' %}" class="btn btn-outline-primary">
                <i class="bi bi-tools me-2"></i>إدارة الصيانة
            </a>
        </div>
    </div>
</div>
{% endblock %}
