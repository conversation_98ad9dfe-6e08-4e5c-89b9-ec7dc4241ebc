{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .search-filter-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .production-line-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .production-line-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }

    .production-line-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        border-radius: 20px 20px 0 0;
    }

    .production-line-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .line-header {
        display: flex;
        justify-content: between;
        align-items: flex-start;
        margin-bottom: 1.5rem;
    }

    .line-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        margin-bottom: 1rem;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .line-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .line-code {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
        background: rgba(102, 126, 234, 0.1);
        padding: 0.3rem 0.8rem;
        border-radius: 10px;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .line-info {
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
        margin-bottom: 1.5rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .info-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .info-value {
        color: #2c3e50;
        font-weight: 700;
    }

    .status-badge {
        padding: 0.4rem 1rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-operational {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    }

    .status-maintenance {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
    }

    .status-idle {
        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        color: white;
        box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
    }

    .status-setup {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .line-actions {
        display: flex;
        gap: 0.8rem;
        margin-top: 1.5rem;
    }

    .action-btn {
        flex: 1;
        padding: 0.8rem;
        border-radius: 12px;
        text-decoration: none;
        text-align: center;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-outline-custom {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .btn-outline-custom:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .pagination-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 1.5rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-search {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-search:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .empty-state i {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .empty-state h3 {
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .production-line-grid {
            grid-template-columns: 1fr;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .line-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-diagram-3 me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'production:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left me-2"></i>العودة
                    </a>
                    <a href="{% url 'production:production_line_create' %}" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>إنشاء جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="search-filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label fw-bold">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث في الاسم أو الكود..." 
                       value="{{ search_query }}">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-search w-100">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>

    <!-- قائمة خطوط الإنتاج -->
    {% if page_obj %}
    <div class="production-line-grid">
        {% for line in page_obj %}
        <div class="production-line-card">
            <div class="line-icon">
                <i class="bi bi-diagram-3"></i>
            </div>
            
            <div class="line-title">{{ line.name }}</div>
            <div class="line-code">{{ line.code }}</div>
            
            <div class="line-info">
                <div class="info-item">
                    <span class="info-label">المخزن</span>
                    <span class="info-value">{{ line.warehouse.name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الطاقة الإنتاجية</span>
                    <span class="info-value">{{ line.capacity_per_hour }} وحدة/ساعة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">معدل الكفاءة</span>
                    <span class="info-value">{{ line.efficiency_rate }}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة</span>
                    <span class="status-badge status-{{ line.status }}">
                        {{ line.get_status_display }}
                    </span>
                </div>
                {% if line.supervisor %}
                <div class="info-item">
                    <span class="info-label">المشرف</span>
                    <span class="info-value">{{ line.supervisor.get_full_name|default:line.supervisor.username }}</span>
                </div>
                {% endif %}
            </div>

            <div class="line-actions">
                <a href="{% url 'production:production_line_detail' line.pk %}" 
                   class="action-btn btn-primary-custom">
                    <i class="bi bi-eye me-1"></i>عرض
                </a>
                <a href="{% url 'production:production_line_update' line.pk %}" 
                   class="action-btn btn-outline-custom">
                    <i class="bi bi-pencil me-1"></i>تعديل
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- الترقيم -->
    {% if page_obj.has_other_pages %}
    <div class="pagination-container">
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination justify-content-center mb-0">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}">
                        السابق
                    </a>
                </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}">
                        {{ num }}
                    </a>
                </li>
                {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <i class="bi bi-diagram-3"></i>
        <h3>لا توجد خطوط إنتاج</h3>
        <p>لم يتم العثور على خطوط إنتاج تطابق معايير البحث المحددة</p>
        <a href="{% url 'production:production_line_create' %}" class="btn btn-primary btn-lg">
            <i class="bi bi-plus-circle me-2"></i>إنشاء خط إنتاج جديد
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
