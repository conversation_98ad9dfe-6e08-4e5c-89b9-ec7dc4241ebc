{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .search-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .downtime-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .downtime-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .downtime-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .downtime-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(231, 76, 60, 0.1);
    }

    .downtime-title {
        font-size: 1.3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .downtime-info {
        margin-bottom: 1rem;
    }

    .downtime-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;
        padding: 0.5rem;
        background: rgba(231, 76, 60, 0.05);
        border-radius: 10px;
    }

    .downtime-info-label {
        font-weight: 600;
        color: #495057;
    }

    .downtime-info-value {
        font-weight: 500;
        color: #2c3e50;
    }

    .severity-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .severity-low { background: #27ae60; color: white; }
    .severity-medium { background: #f39c12; color: white; }
    .severity-high { background: #e67e22; color: white; }
    .severity-critical { background: #e74c3c; color: white; }
    .severity-emergency { background: #8e44ad; color: white; }

    .downtime-type-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        background: #34495e;
        color: white;
    }

    .ongoing-indicator {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: #e74c3c;
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .duration-display {
        font-size: 1.2rem;
        font-weight: 700;
        color: #e74c3c;
        text-align: center;
        padding: 1rem;
        background: rgba(231, 76, 60, 0.1);
        border-radius: 10px;
        margin: 1rem 0;
    }

    .btn-group-custom {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .btn-custom {
        flex: 1;
        padding: 0.8rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        text-align: center;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        margin: 2rem 0;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-box {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(231, 76, 60, 0.2);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #e74c3c;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-exclamation-triangle me-3"></i>
                    سجلات التوقف
                </h1>
                <p class="text-white-50 mb-0 mt-2">تتبع ومراقبة جميع أوقات التوقف والأعطال</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:downtime_record_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>تسجيل توقف جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-row">
        <div class="stat-box">
            <div class="stat-number">{{ page_obj|length }}</div>
            <div class="stat-label">إجمالي السجلات</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">
                {% for record in page_obj %}
                    {% if record.is_ongoing %}1{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">توقف مستمر</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">
                {% for record in page_obj %}
                    {% if record.duration_hours %}{{ record.duration_hours|add:0 }}{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">إجمالي ساعات التوقف</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">
                {% for record in page_obj %}
                    {% if record.cost_impact %}{{ record.cost_impact|add:0 }}{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">التأثير على التكلفة</div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label fw-bold">نوع التوقف</label>
                <select name="downtime_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for type_code, type_name in downtime_types %}
                    <option value="{{ type_code }}" {% if request.GET.downtime_type == type_code %}selected{% endif %}>
                        {{ type_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label fw-bold">خط الإنتاج</label>
                <select name="production_line" class="form-select">
                    <option value="">جميع خطوط الإنتاج</option>
                    {% for line in production_lines %}
                    <option value="{{ line.id }}" {% if request.GET.production_line == line.id|stringformat:"s" %}selected{% endif %}>
                        {{ line.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label fw-bold">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" 
                       value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>

    <!-- قائمة سجلات التوقف -->
    {% if page_obj %}
    <div class="downtime-grid">
        {% for record in page_obj %}
        <div class="downtime-card">
            {% if record.is_ongoing %}
            <div class="ongoing-indicator">
                <i class="bi bi-clock me-1"></i>مستمر
            </div>
            {% endif %}
            
            <div class="downtime-header">
                <div class="downtime-title">{{ record.title }}</div>
                <div>
                    <span class="downtime-type-badge">{{ record.get_downtime_type_display }}</span>
                </div>
            </div>

            <div class="downtime-info">
                <div class="downtime-info-item">
                    <span class="downtime-info-label">خط الإنتاج:</span>
                    <span class="downtime-info-value">{{ record.production_line.name }}</span>
                </div>
                {% if record.equipment %}
                <div class="downtime-info-item">
                    <span class="downtime-info-label">المعدة:</span>
                    <span class="downtime-info-value">{{ record.equipment.name }}</span>
                </div>
                {% endif %}
                <div class="downtime-info-item">
                    <span class="downtime-info-label">الخطورة:</span>
                    <span class="downtime-info-value">
                        <span class="severity-badge severity-{{ record.severity }}">
                            {{ record.get_severity_display }}
                        </span>
                    </span>
                </div>
                <div class="downtime-info-item">
                    <span class="downtime-info-label">بدء التوقف:</span>
                    <span class="downtime-info-value">{{ record.start_time|date:"Y/m/d H:i" }}</span>
                </div>
                {% if record.end_time %}
                <div class="downtime-info-item">
                    <span class="downtime-info-label">انتهاء التوقف:</span>
                    <span class="downtime-info-value">{{ record.end_time|date:"Y/m/d H:i" }}</span>
                </div>
                {% endif %}
                <div class="downtime-info-item">
                    <span class="downtime-info-label">أبلغ بواسطة:</span>
                    <span class="downtime-info-value">{{ record.reported_by.username|default:"غير محدد" }}</span>
                </div>
            </div>

            {% if record.duration_hours %}
            <div class="duration-display">
                <i class="bi bi-clock me-2"></i>
                المدة: {{ record.duration_hours|floatformat:1 }} ساعة
            </div>
            {% else %}
            <div class="duration-display">
                <i class="bi bi-hourglass-split me-2"></i>
                التوقف مستمر
            </div>
            {% endif %}

            {% if record.description %}
            <div class="mt-2">
                <small class="text-muted">
                    <strong>الوصف:</strong> {{ record.description|truncatewords:15 }}
                </small>
            </div>
            {% endif %}

            <div class="btn-group-custom">
                <a href="#" class="btn-custom btn-primary-custom">
                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                </a>
                {% if record.is_ongoing %}
                <a href="#" class="btn-custom btn-outline-success">
                    <i class="bi bi-check-circle me-2"></i>إنهاء التوقف
                </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    {% else %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="bi bi-exclamation-triangle" style="font-size: 4rem; color: #e74c3c;"></i>
        </div>
        <h3 style="color: #2c3e50;">لا توجد سجلات توقف</h3>
        <p style="color: #6c757d;">لم يتم العثور على سجلات توقف. هذا أمر جيد!</p>
        <a href="{% url 'manufacturing:downtime_record_create' %}" class="btn btn-primary btn-lg">
            <i class="bi bi-plus-circle me-2"></i>تسجيل توقف جديد
        </a>
    </div>
    {% endif %}

    <!-- أزرار العودة -->
    <div class="text-center mt-4">
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                <i class="bi bi-house me-2"></i>لوحة التحكم
            </a>
            <a href="{% url 'manufacturing:performance_dashboard' %}" class="btn btn-outline-primary">
                <i class="bi bi-speedometer2 me-2"></i>مراقبة الأداء
            </a>
        </div>
    </div>
</div>
{% endblock %}
