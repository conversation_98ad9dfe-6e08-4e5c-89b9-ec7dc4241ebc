from django import forms
from django.forms import inlineformset_factory
from django.contrib.auth.models import User
from definitions.models import ProductDefinition, WarehouseDefinition
from .models import (
    ProductionLine, BillOfMaterials, BOMItem, ProductionOrder,
    ProductionOrderItem, QualityCheck, ProductionReport,
    Equipment, EquipmentCategory, MaintenanceType, MaintenanceSchedule,
    MaintenanceRecord, PerformanceMetric, DowntimeRecord
)


class ProductionLineForm(forms.ModelForm):
    """نموذج خط الإنتاج"""
    class Meta:
        model = ProductionLine
        fields = [
            'name', 'code', 'description', 'warehouse', 'capacity_per_hour',
            'efficiency_rate', 'status', 'installation_date', 'last_maintenance',
            'next_maintenance', 'supervisor'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم خط الإنتاج'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'كود خط الإنتاج'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف خط الإنتاج'
            }),
            'warehouse': forms.Select(attrs={
                'class': 'form-select'
            }),
            'capacity_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01'
            }),
            'efficiency_rate': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'installation_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'last_maintenance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'next_maintenance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'supervisor': forms.Select(attrs={
                'class': 'form-select'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['warehouse'].queryset = WarehouseDefinition.objects.filter(is_active=True)
        self.fields['supervisor'].queryset = User.objects.filter(is_active=True)


class BillOfMaterialsForm(forms.ModelForm):
    """نموذج قائمة المواد"""
    class Meta:
        model = BillOfMaterials
        fields = [
            'name', 'code', 'product', 'version', 'quantity_produced',
            'production_time_minutes', 'labor_cost', 'overhead_cost',
            'status', 'effective_date', 'expiry_date', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم قائمة المواد'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'كود قائمة المواد'
            }),
            'product': forms.Select(attrs={
                'class': 'form-select'
            }),
            'version': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '1.0'
            }),
            'quantity_produced': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'production_time_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'labor_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'overhead_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'effective_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'expiry_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = ProductDefinition.objects.filter(is_active=True)


class BOMItemForm(forms.ModelForm):
    """نموذج عنصر قائمة المواد"""
    class Meta:
        model = BOMItem
        fields = [
            'material', 'item_type', 'quantity_required', 'unit_cost',
            'waste_percentage', 'is_critical', 'supplier_lead_time',
            'notes', 'sequence'
        ]
        widgets = {
            'material': forms.Select(attrs={
                'class': 'form-select'
            }),
            'item_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'quantity_required': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'waste_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'is_critical': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'supplier_lead_time': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات'
            }),
            'sequence': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['material'].queryset = ProductDefinition.objects.filter(is_active=True)


class ProductionOrderForm(forms.ModelForm):
    """نموذج أمر الإنتاج"""
    class Meta:
        model = ProductionOrder
        fields = [
            'order_number', 'bom', 'production_line', 'quantity_ordered',
            'status', 'priority', 'planned_start_date', 'planned_end_date',
            'customer_order_ref', 'notes'
        ]
        widgets = {
            'order_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الأمر'
            }),
            'bom': forms.Select(attrs={
                'class': 'form-select'
            }),
            'production_line': forms.Select(attrs={
                'class': 'form-select'
            }),
            'quantity_ordered': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'planned_start_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'planned_end_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'customer_order_ref': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مرجع طلب العميل'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['bom'].queryset = BillOfMaterials.objects.filter(status='active')
        self.fields['production_line'].queryset = ProductionLine.objects.filter(is_active=True)


class QualityCheckForm(forms.ModelForm):
    """نموذج فحص الجودة"""
    class Meta:
        model = QualityCheck
        fields = [
            'check_type', 'inspector', 'quantity_checked', 'quantity_passed',
            'quantity_failed', 'status', 'defect_description',
            'corrective_action', 'notes'
        ]
        widgets = {
            'check_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'inspector': forms.Select(attrs={
                'class': 'form-select'
            }),
            'quantity_checked': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'quantity_passed': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0'
            }),
            'quantity_failed': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'defect_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف العيوب'
            }),
            'corrective_action': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'الإجراء التصحيحي'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['inspector'].queryset = User.objects.filter(is_active=True)


class ProductionReportForm(forms.ModelForm):
    """نموذج تقرير الإنتاج"""
    class Meta:
        model = ProductionReport
        fields = [
            'report_date', 'shift', 'operator', 'quantity_produced',
            'quantity_scrapped', 'downtime_minutes', 'downtime_reason',
            'efficiency_rate', 'notes'
        ]
        widgets = {
            'report_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'shift': forms.Select(attrs={
                'class': 'form-select'
            }),
            'operator': forms.Select(attrs={
                'class': 'form-select'
            }),
            'quantity_produced': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0'
            }),
            'quantity_scrapped': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0'
            }),
            'downtime_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'downtime_reason': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'سبب التوقف'
            }),
            'efficiency_rate': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['operator'].queryset = User.objects.filter(is_active=True)


# إنشاء Formsets
BOMItemFormSet = inlineformset_factory(
    BillOfMaterials,
    BOMItem,
    form=BOMItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)


class ManufacturingSearchForm(forms.Form):
    """نموذج البحث في التصنيع"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث...'
        })
    )

    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


# نماذج المعدات والصيانة
class EquipmentCategoryForm(forms.ModelForm):
    """نموذج فئة المعدات"""
    class Meta:
        model = EquipmentCategory
        fields = ['name', 'code', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم فئة المعدات'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود الفئة'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class EquipmentForm(forms.ModelForm):
    """نموذج المعدات"""
    class Meta:
        model = Equipment
        fields = [
            'name', 'code', 'category', 'production_line', 'manufacturer',
            'model', 'serial_number', 'year_manufactured', 'rated_capacity',
            'capacity_unit', 'power_consumption', 'status', 'condition',
            'installation_date', 'last_maintenance_date', 'next_maintenance_date',
            'purchase_cost', 'current_value', 'location', 'notes', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المعدة'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود المعدة'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'production_line': forms.Select(attrs={'class': 'form-select'}),
            'manufacturer': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الشركة المصنعة'}),
            'model': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الموديل'}),
            'serial_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرقم التسلسلي'}),
            'year_manufactured': forms.NumberInput(attrs={'class': 'form-control', 'min': '1900', 'max': '2030'}),
            'rated_capacity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'capacity_unit': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'وحدة/ساعة'}),
            'power_consumption': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'condition': forms.Select(attrs={'class': 'form-select'}),
            'installation_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'last_maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'next_maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'purchase_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'current_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'موقع المعدة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def clean_next_maintenance_date(self):
        next_maintenance = self.cleaned_data.get('next_maintenance_date')
        last_maintenance = self.cleaned_data.get('last_maintenance_date')

        if next_maintenance and last_maintenance:
            if next_maintenance <= last_maintenance:
                raise forms.ValidationError('تاريخ الصيانة القادمة يجب أن يكون بعد تاريخ آخر صيانة')

        return next_maintenance


class MaintenanceTypeForm(forms.ModelForm):
    """نموذج نوع الصيانة"""
    class Meta:
        model = MaintenanceType
        fields = ['name', 'code', 'description', 'is_preventive', 'estimated_duration_hours', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم نوع الصيانة'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود نوع الصيانة'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_preventive': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'estimated_duration_hours': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class MaintenanceScheduleForm(forms.ModelForm):
    """نموذج جدولة الصيانة"""
    class Meta:
        model = MaintenanceSchedule
        fields = [
            'equipment', 'maintenance_type', 'frequency', 'frequency_value',
            'operating_hours_interval', 'cycles_interval', 'last_maintenance_date',
            'next_maintenance_date', 'is_active', 'notes'
        ]
        widgets = {
            'equipment': forms.Select(attrs={'class': 'form-select'}),
            'maintenance_type': forms.Select(attrs={'class': 'form-select'}),
            'frequency': forms.Select(attrs={'class': 'form-select'}),
            'frequency_value': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'operating_hours_interval': forms.NumberInput(attrs={'class': 'form-control'}),
            'cycles_interval': forms.NumberInput(attrs={'class': 'form-control'}),
            'last_maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'next_maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class MaintenanceRecordForm(forms.ModelForm):
    """نموذج سجل الصيانة"""
    class Meta:
        model = MaintenanceRecord
        fields = [
            'equipment', 'maintenance_type', 'schedule', 'title', 'description',
            'priority', 'status', 'scheduled_date', 'actual_start_date',
            'actual_end_date', 'estimated_duration_hours', 'actual_duration_hours',
            'assigned_technician', 'performed_by', 'labor_cost', 'parts_cost',
            'external_cost', 'work_performed', 'parts_replaced', 'findings',
            'recommendations'
        ]
        widgets = {
            'equipment': forms.Select(attrs={'class': 'form-select'}),
            'maintenance_type': forms.Select(attrs={'class': 'form-select'}),
            'schedule': forms.Select(attrs={'class': 'form-select'}),
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان الصيانة'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'priority': forms.Select(attrs={'class': 'form-select'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'scheduled_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'actual_start_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'actual_end_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'estimated_duration_hours': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'actual_duration_hours': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'assigned_technician': forms.Select(attrs={'class': 'form-select'}),
            'performed_by': forms.Select(attrs={'class': 'form-select'}),
            'labor_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'parts_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'external_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'work_performed': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'parts_replaced': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'findings': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'recommendations': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class PerformanceMetricForm(forms.ModelForm):
    """نموذج مقياس الأداء"""
    class Meta:
        model = PerformanceMetric
        fields = [
            'production_line', 'equipment', 'metric_type', 'measurement_date',
            'shift', 'target_value', 'actual_value', 'unit', 'notes'
        ]
        widgets = {
            'production_line': forms.Select(attrs={'class': 'form-select'}),
            'equipment': forms.Select(attrs={'class': 'form-select'}),
            'metric_type': forms.Select(attrs={'class': 'form-select'}),
            'measurement_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'shift': forms.Select(attrs={'class': 'form-select'}),
            'target_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.001'}),
            'actual_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.001'}),
            'unit': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الوحدة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class DowntimeRecordForm(forms.ModelForm):
    """نموذج سجل التوقف"""
    class Meta:
        model = DowntimeRecord
        fields = [
            'production_line', 'equipment', 'title', 'downtime_type', 'severity',
            'start_time', 'end_time', 'description', 'root_cause',
            'corrective_action', 'preventive_action', 'reported_by',
            'resolved_by', 'production_loss', 'cost_impact'
        ]
        widgets = {
            'production_line': forms.Select(attrs={'class': 'form-select'}),
            'equipment': forms.Select(attrs={'class': 'form-select'}),
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان التوقف'}),
            'downtime_type': forms.Select(attrs={'class': 'form-select'}),
            'severity': forms.Select(attrs={'class': 'form-select'}),
            'start_time': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'end_time': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'root_cause': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'corrective_action': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'preventive_action': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'reported_by': forms.Select(attrs={'class': 'form-select'}),
            'resolved_by': forms.Select(attrs={'class': 'form-select'}),
            'production_loss': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.001'}),
            'cost_impact': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        }

    def clean_end_time(self):
        start_time = self.cleaned_data.get('start_time')
        end_time = self.cleaned_data.get('end_time')

        if end_time and start_time:
            if end_time <= start_time:
                raise forms.ValidationError('وقت الانتهاء يجب أن يكون بعد وقت البدء')

        return end_time


