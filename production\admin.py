from django.contrib import admin
from .models import (
    ProductionLine, WorkCenter, BillOfMaterials, BOMItem, ProductionOrder
)


@admin.register(ProductionLine)
class ProductionLineAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'warehouse', 'capacity_per_hour', 'efficiency_rate', 'status', 'is_active']
    list_filter = ['status', 'is_active', 'warehouse']
    search_fields = ['name', 'code']
    readonly_fields = ['created_at', 'updated_at', 'age_in_years', 'equipment_count', 'active_equipment_count']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'code', 'description', 'warehouse', 'supervisor', 'is_active')
        }),
        ('المواصفات التقنية', {
            'fields': ('capacity_per_hour', 'efficiency_rate', 'installation_date', 'status')
        }),
        ('المعلومات المالية', {
            'fields': ('setup_cost', 'operating_cost_per_hour')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'age_in_years', 'equipment_count', 'active_equipment_count'),
            'classes': ('collapse',)
        }),
    )


@admin.register(WorkCenter)
class WorkCenterAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'production_line', 'capacity_per_hour', 'is_active']
    list_filter = ['production_line', 'is_active']
    search_fields = ['name', 'code', 'production_line__name']
    readonly_fields = ['created_at', 'updated_at']


class BOMItemInline(admin.TabularInline):
    model = BOMItem
    extra = 1
    fields = ['sequence', 'material', 'item_type', 'quantity_required', 'unit_cost',
              'waste_percentage', 'is_critical', 'supplier']
    readonly_fields = ['total_cost', 'effective_quantity']


@admin.register(BillOfMaterials)
class BillOfMaterialsAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'product', 'version', 'status', 'effective_date', 'total_cost']
    list_filter = ['status', 'effective_date', 'product__category']
    search_fields = ['name', 'code', 'product__name']
    readonly_fields = ['created_at', 'updated_at', 'total_material_cost', 'total_cost', 'unit_cost', 'is_active_now']
    inlines = [BOMItemInline]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'code', 'product', 'version', 'status', 'is_active')
        }),
        ('الكميات والأوقات', {
            'fields': ('quantity_produced', 'production_time_minutes')
        }),
        ('التكاليف', {
            'fields': ('labor_cost', 'overhead_cost', 'total_material_cost', 'total_cost', 'unit_cost')
        }),
        ('التواريخ', {
            'fields': ('effective_date', 'expiry_date')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'is_active_now'),
            'classes': ('collapse',)
        }),
    )


@admin.register(BOMItem)
class BOMItemAdmin(admin.ModelAdmin):
    list_display = ['material', 'bom', 'sequence', 'quantity_required', 'unit_cost', 'total_cost', 'is_critical']
    list_filter = ['item_type', 'is_critical', 'bom__product']
    search_fields = ['material__name', 'bom__name']
    readonly_fields = ['total_cost', 'effective_quantity']


@admin.register(ProductionOrder)
class ProductionOrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'bom', 'production_line', 'quantity_ordered', 'quantity_produced',
                   'status', 'priority', 'completion_percentage']
    list_filter = ['status', 'priority', 'production_line', 'planned_start_date']
    search_fields = ['order_number', 'bom__name', 'bom__product__name']
    readonly_fields = ['created_at', 'updated_at', 'completion_percentage', 'is_overdue', 'days_remaining']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('order_number', 'bom', 'production_line', 'status', 'priority', 'is_active')
        }),
        ('الكميات', {
            'fields': ('quantity_ordered', 'quantity_produced', 'completion_percentage')
        }),
        ('التواريخ المخططة', {
            'fields': ('planned_start_date', 'planned_end_date')
        }),
        ('التواريخ الفعلية', {
            'fields': ('actual_start_date', 'actual_end_date')
        }),
        ('المسؤولون', {
            'fields': ('planner', 'supervisor')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'is_overdue', 'days_remaining'),
            'classes': ('collapse',)
        }),
    )
