# Generated by Django 5.2.4 on 2025-07-19 12:22

import datetime
import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0014_auto_20250714_0411'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BillOfMaterials',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('name', models.CharField(max_length=200, verbose_name='اسم قائمة المواد')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود قائمة المواد')),
                ('version', models.CharField(default='1.0', max_length=20, verbose_name='الإصدار')),
                ('quantity_produced', models.DecimalField(decimal_places=3, default=1, max_digits=10, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المنتجة')),
                ('production_time_minutes', models.PositiveIntegerField(verbose_name='وقت الإنتاج (دقيقة)')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='تكلفة العمالة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='التكاليف الإضافية')),
                ('effective_date', models.DateField(default=datetime.date.today, verbose_name='تاريخ السريان')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('active', 'نشطة'), ('inactive', 'غير نشطة'), ('archived', 'مؤرشفة')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_lists', to='definitions.productdefinition', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'قائمة مواد',
                'verbose_name_plural': 'قوائم المواد',
                'ordering': ['-effective_date', 'product__name'],
                'unique_together': {('product', 'version')},
            },
        ),
        migrations.CreateModel(
            name='ProductionLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('name', models.CharField(max_length=200, verbose_name='اسم خط الإنتاج')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود خط الإنتاج')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('capacity_per_hour', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الطاقة الإنتاجية/ساعة')),
                ('efficiency_rate', models.DecimalField(decimal_places=2, default=85.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='معدل الكفاءة %')),
                ('installation_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التركيب')),
                ('status', models.CharField(choices=[('operational', 'تشغيلي'), ('maintenance', 'صيانة'), ('idle', 'متوقف'), ('setup', 'إعداد')], default='operational', max_length=20, verbose_name='الحالة')),
                ('setup_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='تكلفة الإنشاء')),
                ('operating_cost_per_hour', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='التكلفة التشغيلية/ساعة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_lines', to=settings.AUTH_USER_MODEL, verbose_name='المشرف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_lines', to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'خط إنتاج',
                'verbose_name_plural': 'خطوط الإنتاج',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductionOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الأمر')),
                ('quantity_ordered', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المطلوبة')),
                ('quantity_produced', models.DecimalField(decimal_places=3, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المنتجة')),
                ('planned_start_date', models.DateTimeField(verbose_name='تاريخ البدء المخطط')),
                ('planned_end_date', models.DateTimeField(verbose_name='تاريخ الانتهاء المخطط')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البدء الفعلي')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء الفعلي')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('planned', 'مخطط'), ('released', 'محرر'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('on_hold', 'معلق')], default='draft', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_orders', to='production.billofmaterials', verbose_name='قائمة المواد')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('planner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='planned_orders', to=settings.AUTH_USER_MODEL, verbose_name='المخطط')),
                ('production_line', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_orders', to='production.productionline', verbose_name='خط الإنتاج')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_orders', to=settings.AUTH_USER_MODEL, verbose_name='المشرف')),
            ],
            options={
                'verbose_name': 'أمر إنتاج',
                'verbose_name_plural': 'أوامر الإنتاج',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WorkCenter',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('name', models.CharField(max_length=200, verbose_name='اسم مركز العمل')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود مركز العمل')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('capacity_per_hour', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الطاقة الإنتاجية/ساعة')),
                ('setup_time_minutes', models.PositiveIntegerField(default=0, verbose_name='وقت الإعداد (دقيقة)')),
                ('labor_cost_per_hour', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='تكلفة العمالة/ساعة')),
                ('overhead_cost_per_hour', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='التكاليف الإضافية/ساعة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('production_line', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='work_centers', to='production.productionline', verbose_name='خط الإنتاج')),
            ],
            options={
                'verbose_name': 'مركز عمل',
                'verbose_name_plural': 'مراكز العمل',
                'ordering': ['production_line', 'name'],
            },
        ),
        migrations.CreateModel(
            name='BOMItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('sequence', models.PositiveIntegerField(default=1, verbose_name='التسلسل')),
                ('item_type', models.CharField(choices=[('raw_material', 'مادة خام'), ('component', 'مكون'), ('sub_assembly', 'تجميع فرعي'), ('consumable', 'مادة استهلاكية')], default='raw_material', max_length=20, verbose_name='نوع العنصر')),
                ('quantity_required', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المطلوبة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='تكلفة الوحدة')),
                ('waste_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة الفاقد %')),
                ('is_critical', models.BooleanField(default=False, verbose_name='مادة حرجة')),
                ('lead_time_days', models.PositiveIntegerField(default=0, verbose_name='مدة التوريد (يوم)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='production.billofmaterials', verbose_name='قائمة المواد')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_items', to='definitions.productdefinition', verbose_name='المادة')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bom_items', to='definitions.persondefinition', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'عنصر قائمة مواد',
                'verbose_name_plural': 'عناصر قوائم المواد',
                'ordering': ['bom', 'sequence'],
                'unique_together': {('bom', 'material')},
            },
        ),
    ]
