# Generated by Django 5.2.4 on 2025-07-19 11:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('manufacturing', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MaintenanceType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم نوع الصيانة')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='الكود')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_preventive', models.BooleanField(default=True, verbose_name='صيانة وقائية')),
                ('estimated_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, verbose_name='المدة المقدرة (ساعة)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'نوع الصيانة',
                'verbose_name_plural': 'أنواع الصيانة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Equipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المعدة')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المعدة')),
                ('manufacturer', models.CharField(blank=True, max_length=100, verbose_name='الشركة المصنعة')),
                ('model', models.CharField(blank=True, max_length=100, verbose_name='الموديل')),
                ('serial_number', models.CharField(blank=True, max_length=100, verbose_name='الرقم التسلسلي')),
                ('year_manufactured', models.IntegerField(blank=True, null=True, verbose_name='سنة التصنيع')),
                ('rated_capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الطاقة المقدرة')),
                ('capacity_unit', models.CharField(blank=True, max_length=20, verbose_name='وحدة الطاقة')),
                ('power_consumption', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='استهلاك الطاقة (كيلو وات)')),
                ('status', models.CharField(choices=[('operational', 'تشغيلي'), ('maintenance', 'تحت الصيانة'), ('breakdown', 'معطل'), ('idle', 'متوقف'), ('retired', 'خارج الخدمة')], default='operational', max_length=20, verbose_name='الحالة')),
                ('condition', models.CharField(choices=[('excellent', 'ممتاز'), ('good', 'جيد'), ('fair', 'مقبول'), ('poor', 'ضعيف'), ('critical', 'حرج')], default='good', max_length=20, verbose_name='الحالة الفنية')),
                ('installation_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التركيب')),
                ('last_maintenance_date', models.DateField(blank=True, null=True, verbose_name='تاريخ آخر صيانة')),
                ('next_maintenance_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصيانة القادمة')),
                ('purchase_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='تكلفة الشراء')),
                ('current_value', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='القيمة الحالية')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('production_line', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipment', to='manufacturing.productionline', verbose_name='خط الإنتاج')),
            ],
            options={
                'verbose_name': 'معدة',
                'verbose_name_plural': 'المعدات',
                'ordering': ['production_line', 'name'],
            },
        ),
        migrations.CreateModel(
            name='DowntimeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التوقف')),
                ('downtime_type', models.CharField(choices=[('planned', 'توقف مخطط'), ('unplanned', 'توقف غير مخطط'), ('maintenance', 'صيانة'), ('breakdown', 'عطل'), ('changeover', 'تغيير المنتج'), ('material_shortage', 'نقص المواد'), ('quality_issue', 'مشكلة جودة'), ('operator_absence', 'غياب المشغل'), ('power_outage', 'انقطاع الكهرباء'), ('other', 'أخرى')], max_length=20, verbose_name='نوع التوقف')),
                ('severity', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('critical', 'حرجة')], default='medium', max_length=10, verbose_name='الخطورة')),
                ('start_time', models.DateTimeField(verbose_name='وقت البدء')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت الانتهاء')),
                ('duration_minutes', models.IntegerField(blank=True, null=True, verbose_name='المدة (دقيقة)')),
                ('description', models.TextField(verbose_name='وصف التوقف')),
                ('root_cause', models.TextField(blank=True, verbose_name='السبب الجذري')),
                ('corrective_action', models.TextField(blank=True, verbose_name='الإجراء التصحيحي')),
                ('preventive_action', models.TextField(blank=True, verbose_name='الإجراء الوقائي')),
                ('production_loss', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='خسارة الإنتاج')),
                ('cost_impact', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التأثير على التكلفة')),
                ('is_resolved', models.BooleanField(default=False, verbose_name='محلول')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('production_line', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='downtime_records', to='manufacturing.productionline', verbose_name='خط الإنتاج')),
                ('reported_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reported_downtimes', to=settings.AUTH_USER_MODEL, verbose_name='أبلغ بواسطة')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_downtimes', to=settings.AUTH_USER_MODEL, verbose_name='حل بواسطة')),
                ('equipment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='downtime_records', to='manufacturing.equipment', verbose_name='المعدة')),
            ],
            options={
                'verbose_name': 'سجل توقف',
                'verbose_name_plural': 'سجلات التوقف',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='EquipmentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الفئة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'فئة المعدات',
                'verbose_name_plural': 'فئات المعدات',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='equipment',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.equipmentcategory', verbose_name='الفئة'),
        ),
        migrations.CreateModel(
            name='MaintenanceSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('semi_annual', 'نصف سنوي'), ('annual', 'سنوي'), ('hours_based', 'حسب ساعات التشغيل'), ('cycles_based', 'حسب دورات التشغيل')], max_length=20, verbose_name='التكرار')),
                ('frequency_value', models.IntegerField(default=1, verbose_name='قيمة التكرار')),
                ('operating_hours_interval', models.IntegerField(blank=True, null=True, verbose_name='فترة ساعات التشغيل')),
                ('cycles_interval', models.IntegerField(blank=True, null=True, verbose_name='فترة الدورات')),
                ('last_maintenance_date', models.DateField(blank=True, null=True, verbose_name='تاريخ آخر صيانة')),
                ('next_maintenance_date', models.DateField(verbose_name='تاريخ الصيانة القادمة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_schedules', to='manufacturing.equipment', verbose_name='المعدة')),
                ('maintenance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.maintenancetype', verbose_name='نوع الصيانة')),
            ],
            options={
                'verbose_name': 'جدولة صيانة',
                'verbose_name_plural': 'جدولة الصيانة',
                'ordering': ['next_maintenance_date'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الصيانة')),
                ('description', models.TextField(verbose_name='وصف الصيانة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة'), ('emergency', 'طوارئ')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغية'), ('postponed', 'مؤجلة')], default='scheduled', max_length=20, verbose_name='الحالة')),
                ('scheduled_date', models.DateTimeField(verbose_name='التاريخ المجدول')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البدء الفعلي')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء الفعلي')),
                ('estimated_duration_hours', models.DecimalField(decimal_places=2, max_digits=6, verbose_name='المدة المقدرة (ساعة)')),
                ('actual_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, verbose_name='المدة الفعلية (ساعة)')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة العمالة')),
                ('parts_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة القطع')),
                ('external_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='التكلفة الخارجية')),
                ('work_performed', models.TextField(blank=True, verbose_name='العمل المنجز')),
                ('parts_replaced', models.TextField(blank=True, verbose_name='القطع المستبدلة')),
                ('findings', models.TextField(blank=True, verbose_name='الملاحظات والنتائج')),
                ('recommendations', models.TextField(blank=True, verbose_name='التوصيات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_technician', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_maintenances', to=settings.AUTH_USER_MODEL, verbose_name='الفني المكلف')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_maintenances', to=settings.AUTH_USER_MODEL)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_records', to='manufacturing.equipment', verbose_name='المعدة')),
                ('performed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='performed_maintenances', to=settings.AUTH_USER_MODEL, verbose_name='نفذ بواسطة')),
                ('schedule', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='manufacturing.maintenanceschedule', verbose_name='الجدولة')),
                ('maintenance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.maintenancetype', verbose_name='نوع الصيانة')),
            ],
            options={
                'verbose_name': 'سجل صيانة',
                'verbose_name_plural': 'سجلات الصيانة',
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_type', models.CharField(choices=[('oee', 'الكفاءة الإجمالية للمعدات (OEE)'), ('availability', 'معدل التوفر'), ('performance', 'معدل الأداء'), ('quality', 'معدل الجودة'), ('throughput', 'معدل الإنتاجية'), ('downtime', 'وقت التوقف'), ('efficiency', 'الكفاءة'), ('utilization', 'معدل الاستخدام')], max_length=20, verbose_name='نوع المقياس')),
                ('measurement_date', models.DateField(verbose_name='تاريخ القياس')),
                ('shift', models.CharField(blank=True, choices=[('morning', 'الصباحية'), ('evening', 'المسائية'), ('night', 'الليلية')], max_length=10, null=True, verbose_name='الوردية')),
                ('target_value', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='القيمة المستهدفة')),
                ('actual_value', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='القيمة الفعلية')),
                ('unit', models.CharField(max_length=20, verbose_name='الوحدة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('equipment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='performance_metrics', to='manufacturing.equipment', verbose_name='المعدة')),
                ('production_line', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_metrics', to='manufacturing.productionline', verbose_name='خط الإنتاج')),
            ],
            options={
                'verbose_name': 'مقياس أداء',
                'verbose_name_plural': 'مقاييس الأداء',
                'ordering': ['-measurement_date', 'production_line'],
                'unique_together': {('production_line', 'equipment', 'metric_type', 'measurement_date', 'shift')},
            },
        ),
    ]
