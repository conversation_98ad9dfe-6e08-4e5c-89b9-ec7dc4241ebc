{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    /* Manufacturing Dashboard - Modern & Colorful */
    :root {
        --primary-gradient: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
        --secondary-gradient: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);
        --success-gradient: linear-gradient(135deg, #54a0ff 0%, #5f27cd 100%);
        --warning-gradient: linear-gradient(135deg, #ff9f43 0%, #ff6348 100%);
        --info-gradient: linear-gradient(135deg, #0abde3 0%, #006ba6 100%);
        --purple-gradient: linear-gradient(135deg, #a55eea 0%, #26de81 100%);
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    }

    .dashboard-container {
        padding: 2rem 0;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 3rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        animation: rotate 10s linear infinite;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .page-title {
        color: white;
        font-size: 3rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
    }

    .page-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.2rem;
        margin: 1rem 0 0 0;
        position: relative;
        z-index: 2;
    }

    /* Stats Cards - Modern & Colorful */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2.5rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent);
        transition: left 0.6s ease;
    }

    .stat-card:hover::before {
        left: 100%;
    }

    .stat-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.15),
            0 0 50px rgba(255, 107, 107, 0.2);
    }

    .stat-icon {
        position: absolute;
        top: 2rem;
        right: 2rem;
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        animation: iconFloat 4s ease-in-out infinite;
    }

    @keyframes iconFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(5deg); }
    }

    .stat-number {
        font-size: 3.5rem;
        font-weight: 900;
        margin-bottom: 1rem;
        line-height: 1;
        animation: numberGlow 3s ease-in-out infinite alternate;
    }

    @keyframes numberGlow {
        0% { filter: brightness(1); }
        100% { filter: brightness(1.2); }
    }

    .stat-label {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .stat-change {
        font-size: 1rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .stat-change.positive {
        background: rgba(46, 204, 113, 0.2);
        color: #27ae60;
        border: 1px solid rgba(46, 204, 113, 0.4);
    }

    .stat-change.negative {
        background: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
        border: 1px solid rgba(231, 76, 60, 0.4);
    }

    /* Specific card colors */
    .stat-card-1 .stat-number {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-card-1 .stat-icon {
        background: var(--primary-gradient);
    }

    .stat-card-2 .stat-number {
        background: var(--secondary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-card-2 .stat-icon {
        background: var(--secondary-gradient);
    }

    .stat-card-3 .stat-number {
        background: var(--success-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-card-3 .stat-icon {
        background: var(--success-gradient);
    }

    .stat-card-4 .stat-number {
        background: var(--warning-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-card-4 .stat-icon {
        background: var(--warning-gradient);
    }

    /* Quick Actions - Modern */
    .actions-section {
        margin: 3rem 0;
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
    }

    .action-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
        transition: all 0.4s ease;
        display: block;
        position: relative;
        overflow: hidden;
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.6s;
    }

    .action-card:hover::before {
        left: 100%;
    }

    .action-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
        border-color: rgba(255, 255, 255, 0.4);
    }

    .action-icon {
        font-size: 3rem;
        margin-bottom: 1.5rem;
        display: block;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .action-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.8rem;
    }

    .action-desc {
        font-size: 1rem;
        opacity: 0.9;
        line-height: 1.5;
    }

    /* Data Sections */
    .data-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 800;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .section-title i {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .table-modern {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .table-modern thead {
        background: var(--primary-gradient);
        color: white;
    }

    .table-modern th {
        border: none;
        padding: 1.2rem;
        font-weight: 700;
        text-transform: uppercase;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }

    .table-modern td {
        border: none;
        padding: 1.2rem;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
    }

    .table-modern tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: scale(1.01);
    }

    /* Status Badges - Colorful */
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: 2px solid transparent;
    }

    .status-draft {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
    }
    .status-planned {
        background: linear-gradient(135deg, #a29bfe, #6c5ce7);
        color: white;
    }
    .status-released {
        background: linear-gradient(135deg, #fd79a8, #e84393);
        color: white;
    }
    .status-in-progress {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        color: white;
    }
    .status-completed {
        background: linear-gradient(135deg, #00b894, #00cec9);
        color: white;
    }
    .status-cancelled {
        background: linear-gradient(135deg, #d63031, #e17055);
        color: white;
    }

    /* Priority Badges */
    .priority-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .priority-low {
        background: linear-gradient(135deg, #00b894, #00cec9);
        color: white;
    }
    .priority-normal {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
    }
    .priority-high {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        color: white;
    }
    .priority-urgent {
        background: linear-gradient(135deg, #d63031, #e17055);
        color: white;
    }

    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Floating Elements */
    .floating-elements {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
    }

    .floating-element {
        position: absolute;
        width: 8px;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: float 8s ease-in-out infinite;
    }

    .floating-element:nth-child(1) { left: 10%; animation-delay: 0s; }
    .floating-element:nth-child(2) { left: 20%; animation-delay: 2s; }
    .floating-element:nth-child(3) { left: 30%; animation-delay: 4s; }
    .floating-element:nth-child(4) { left: 40%; animation-delay: 6s; }
    .floating-element:nth-child(5) { left: 50%; animation-delay: 8s; }

    @keyframes float {
        0%, 100% {
            transform: translateY(100vh) scale(0);
            opacity: 0;
        }
        10% {
            opacity: 1;
            transform: scale(1);
        }
        90% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            transform: translateY(-100px) scale(0);
            opacity: 0;
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .actions-grid {
            grid-template-columns: 1fr;
        }

        .page-title {
            font-size: 2.2rem;
        }

        .stat-number {
            font-size: 2.8rem;
        }

        .page-header {
            padding: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="page-header animate-fade-in">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="bi bi-gear-fill me-3"></i>
                        {% if user_language == 'en' %}Manufacturing Dashboard{% else %}لوحة تحكم التصنيع{% endif %}
                    </h1>
                    <p class="page-subtitle">
                        {% if user_language == 'en' %}Advanced production management system{% else %}نظام إدارة الإنتاج المتقدم{% endif %}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end flex-wrap">
                        <a href="/" class="btn btn-outline-light">
                            <i class="bi bi-house me-2"></i>الرئيسية
                        </a>
                        <a href="{% url 'manufacturing:production_order_create' %}" class="btn btn-light btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>أمر إنتاج جديد
                        </a>
                        <a href="{% url 'manufacturing:bom_create' %}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-list-check me-2"></i>قائمة مواد جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="stats-grid animate-fade-in">
            <!-- خطوط الإنتاج -->
            <div class="stat-card stat-card-1">
                <div class="stat-icon">
                    <i class="bi bi-diagram-3"></i>
                </div>
                <div class="stat-number">{{ active_production_lines|default:0 }}</div>
                <div class="stat-label">خطوط الإنتاج النشطة</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> من إجمالي {{ total_production_lines|default:0 }}
                </div>
            </div>

            <!-- قوائم المواد -->
            <div class="stat-card stat-card-2">
                <div class="stat-icon">
                    <i class="bi bi-list-check"></i>
                </div>
                <div class="stat-number">{{ active_boms|default:0 }}</div>
                <div class="stat-label">قوائم المواد النشطة</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> من إجمالي {{ total_boms|default:0 }}
                </div>
            </div>

            <!-- أوامر الإنتاج -->
            <div class="stat-card stat-card-3">
                <div class="stat-icon">
                    <i class="bi bi-clipboard-data"></i>
                </div>
                <div class="stat-number">{{ orders_in_progress|default:0 }}</div>
                <div class="stat-label">أوامر قيد التنفيذ</div>
                <div class="stat-change {% if orders_overdue > 0 %}negative{% else %}positive{% endif %}">
                    {% if orders_overdue > 0 %}
                        <i class="bi bi-exclamation-triangle"></i> {{ orders_overdue }} متأخر
                    {% else %}
                        <i class="bi bi-check-circle"></i> في الموعد
                    {% endif %}
                </div>
            </div>

            <!-- الإنتاج اليوم -->
            <div class="stat-card stat-card-4">
                <div class="stat-icon">
                    <i class="bi bi-graph-up"></i>
                </div>
                <div class="stat-number">{{ orders_completed_today|default:0 }}</div>
                <div class="stat-label">أوامر مكتملة اليوم</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +{{ orders_completed_today|default:0 }}
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="actions-section animate-fade-in">
            <h2 class="section-title">
                <i class="bi bi-lightning-charge"></i>
                {% if user_language == 'en' %}Quick Actions{% else %}الإجراءات السريعة{% endif %}
            </h2>

            <div class="actions-grid">
                <a href="{% url 'manufacturing:production_line_list' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-diagram-3"></i>
                    </div>
                    <div class="action-title">خطوط الإنتاج</div>
                    <div class="action-desc">إدارة وتكوين خطوط الإنتاج والمعدات</div>
                </a>

                <a href="{% url 'manufacturing:bom_list' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-list-check"></i>
                    </div>
                    <div class="action-title">قوائم المواد</div>
                    <div class="action-desc">إنشاء وإدارة قوائم المواد والوصفات</div>
                </a>

                <a href="{% url 'manufacturing:production_order_list' %}" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-clipboard-data"></i>
                    </div>
                    <div class="action-title">أوامر الإنتاج</div>
                    <div class="action-desc">متابعة وإدارة أوامر الإنتاج</div>
                </a>

                <a href="#" class="action-card">
                    <div class="action-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="action-title">تقارير الإنتاج</div>
                    <div class="action-desc">تقارير شاملة عن الأداء والإنتاجية</div>
                </a>
            </div>
        </div>

        <!-- أحدث أوامر الإنتاج -->
        {% if recent_orders %}
        <div class="data-section animate-fade-in">
            <h2 class="section-title">
                <i class="bi bi-clock-history"></i>
                {% if user_language == 'en' %}Recent Production Orders{% else %}أحدث أوامر الإنتاج{% endif %}
            </h2>

            <div class="table-modern">
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الأمر</th>
                            <th>المنتج</th>
                            <th>خط الإنتاج</th>
                            <th>الكمية</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                            <th>تاريخ البدء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in recent_orders %}
                        <tr>
                            <td><strong>{{ order.order_number }}</strong></td>
                            <td>{{ order.bom.product.name }}</td>
                            <td>{{ order.production_line.name }}</td>
                            <td>{{ order.quantity_ordered|floatformat:2 }}</td>
                            <td>
                                <span class="status-badge status-{{ order.status }}">
                                    {{ order.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <span class="priority-badge priority-{{ order.priority }}">
                                    {{ order.get_priority_display }}
                                </span>
                            </td>
                            <td>{{ order.planned_start_date|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{% url 'manufacturing:production_order_detail' order.pk %}"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="text-center mt-3">
                <a href="{% url 'manufacturing:production_order_list' %}" class="btn btn-primary btn-lg">
                    <i class="bi bi-list-ul me-2"></i>عرض جميع أوامر الإنتاج
                </a>
            </div>
        </div>
        {% endif %}

        <!-- خطوط الإنتاج النشطة -->
        {% if production_lines %}
        <div class="data-section animate-fade-in">
            <h2 class="section-title">
                <i class="bi bi-diagram-3"></i>
                {% if user_language == 'en' %}Active Production Lines{% else %}خطوط الإنتاج النشطة{% endif %}
            </h2>

            <div class="row">
                {% for line in production_lines %}
                <div class="col-lg-6 col-xl-4 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--info-gradient);">
                            <i class="bi bi-gear"></i>
                        </div>
                        <h5 class="stat-label">{{ line.name }}</h5>
                        <p class="mb-2"><strong>الكود:</strong> {{ line.code }}</p>
                        <p class="mb-2"><strong>المخزن:</strong> {{ line.warehouse.name }}</p>
                        <p class="mb-2"><strong>الطاقة:</strong> {{ line.capacity_per_hour }}/ساعة</p>
                        <div class="stat-change {% if line.status == 'active' %}positive{% else %}negative{% endif %}">
                            <i class="bi bi-circle-fill"></i> {{ line.get_status_display }}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'manufacturing:production_line_detail' line.pk %}"
                               class="btn btn-sm btn-outline-primary">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <div class="text-center mt-3">
                <a href="{% url 'manufacturing:production_line_list' %}" class="btn btn-primary btn-lg">
                    <i class="bi bi-diagram-3 me-2"></i>عرض جميع خطوط الإنتاج
                </a>
            </div>
        </div>
        {% endif %}

        <!-- التنبيهات -->
        {% if maintenance_alerts > 0 %}
        <div class="data-section animate-fade-in" style="border-left: 5px solid #e74c3c;">
            <h2 class="section-title" style="color: #e74c3c;">
                <i class="bi bi-exclamation-triangle"></i>
                {% if user_language == 'en' %}Alerts{% else %}التنبيهات{% endif %}
            </h2>

            <div class="alert alert-warning">
                <h5><i class="bi bi-tools me-2"></i>تنبيه صيانة</h5>
                <p>يوجد {{ maintenance_alerts }} خط إنتاج يحتاج إلى صيانة.</p>
                <a href="{% url 'manufacturing:production_line_list' %}?status=maintenance"
                   class="btn btn-warning">
                    عرض التفاصيل
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- العناصر العائمة -->
<div class="floating-elements">
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}

// تأثيرات التحميل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات التحميل للبطاقات
    const cards = document.querySelectorAll('.stat-card, .action-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
});
</script>
{% endblock %}
