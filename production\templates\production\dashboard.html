{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .dashboard-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 30px;
        padding: 3rem;
        margin-bottom: 3rem;
        border: 3px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .dashboard-title {
        color: white;
        font-size: 3.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 6px 30px rgba(0, 0, 0, 0.4);
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 1;
    }

    .dashboard-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.3rem;
        font-weight: 500;
        margin-top: 1rem;
        position: relative;
        z-index: 1;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2.5rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        border-radius: 25px 25px 0 0;
    }

    .stat-card:hover {
        transform: translateY(-15px) scale(1.05);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    }

    .stat-icon {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: white;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        background: linear-gradient(135deg, #2c3e50, #34495e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .stat-change {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .stat-change.positive {
        color: #27ae60;
    }

    .stat-change.negative {
        color: #e74c3c;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 3rem;
        margin-bottom: 3rem;
    }

    .main-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .sidebar-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2.5rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .card-title {
        font-size: 1.8rem;
        font-weight: 800;
        background: linear-gradient(135deg, #2c3e50, #34495e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .card-title i {
        color: #667eea;
        font-size: 2rem;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .action-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1.5rem;
        border-radius: 20px;
        text-decoration: none;
        text-align: center;
        font-weight: 700;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .action-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .action-btn i {
        font-size: 2.5rem;
    }

    .recent-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .recent-item {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        padding: 1.5rem;
        border-left: 5px solid #667eea;
        transition: all 0.3s ease;
    }

    .recent-item:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateX(5px);
    }

    .recent-item-title {
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .recent-item-meta {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .status-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-operational { background: #27ae60; color: white; }
    .status-maintenance { background: #f39c12; color: white; }
    .status-idle { background: #95a5a6; color: white; }
    .status-active { background: #3498db; color: white; }
    .status-draft { background: #95a5a6; color: white; }
    .status-planned { background: #f39c12; color: white; }
    .status-in_progress { background: #3498db; color: white; }
    .status-completed { background: #27ae60; color: white; }

    @media (max-width: 1200px) {
        .content-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .dashboard-title {
            font-size: 2.5rem;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .quick-actions {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس لوحة التحكم -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="dashboard-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    إدارة الإنتاج
                </h1>
                <p class="dashboard-subtitle">
                    نظام شامل لإدارة خطوط الإنتاج وقوائم المواد وأوامر التصنيع
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'home' %}" class="btn btn-outline-light btn-lg">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'production:reports_dashboard' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-graph-up me-2"></i>التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-diagram-3"></i>
            </div>
            <div class="stat-number">{{ total_production_lines }}</div>
            <div class="stat-label">إجمالي خطوط الإنتاج</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                {{ active_production_lines }} نشط
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-list-check"></i>
            </div>
            <div class="stat-number">{{ total_boms }}</div>
            <div class="stat-label">قوائم المواد</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                {{ active_boms }} نشطة
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-clipboard-check"></i>
            </div>
            <div class="stat-number">{{ total_orders }}</div>
            <div class="stat-label">أوامر الإنتاج</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                {{ active_orders }} نشط
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-number">{{ completed_orders_today }}</div>
            <div class="stat-label">مكتمل اليوم</div>
            {% if overdue_orders > 0 %}
            <div class="stat-change negative">
                <i class="bi bi-exclamation-triangle"></i>
                {{ overdue_orders }} متأخر
            </div>
            {% else %}
            <div class="stat-change positive">
                <i class="bi bi-check"></i>
                لا توجد متأخرة
            </div>
            {% endif %}
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="content-grid">
        <!-- العمود الرئيسي -->
        <div class="main-content">
            <!-- إجراءات سريعة -->
            <div class="content-card">
                <h3 class="card-title">
                    <i class="bi bi-lightning-charge"></i>
                    إجراءات سريعة
                </h3>
                <div class="quick-actions">
                    <a href="{% url 'production:production_line_create' %}" class="action-btn">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء خط إنتاج
                    </a>
                    <a href="{% url 'production:bom_create' %}" class="action-btn">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء قائمة مواد
                    </a>
                    <a href="{% url 'production:production_order_create' %}" class="action-btn">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء أمر إنتاج
                    </a>
                    <a href="{% url 'production:work_center_create' %}" class="action-btn">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء مركز عمل
                    </a>
                </div>
            </div>

            <!-- أحدث أوامر الإنتاج -->
            <div class="content-card">
                <h3 class="card-title">
                    <i class="bi bi-clock-history"></i>
                    أحدث أوامر الإنتاج
                </h3>
                <div class="recent-list">
                    {% for order in recent_orders %}
                    <div class="recent-item">
                        <div class="recent-item-title">
                            <a href="{% url 'production:production_order_detail' order.pk %}" 
                               style="text-decoration: none; color: inherit;">
                                {{ order.order_number }} - {{ order.bom.product.name }}
                            </a>
                        </div>
                        <div class="recent-item-meta">
                            <span>{{ order.production_line.name }}</span>
                            <span class="status-badge status-{{ order.status }}">
                                {{ order.get_status_display }}
                            </span>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <p class="mt-2">لا توجد أوامر إنتاج حتى الآن</p>
                    </div>
                    {% endfor %}
                </div>
                {% if recent_orders %}
                <div class="text-center mt-3">
                    <a href="{% url 'production:production_order_list' %}" class="btn btn-outline-primary">
                        عرض جميع الأوامر
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- العمود الجانبي -->
        <div class="sidebar-content">
            <!-- خطوط الإنتاج النشطة -->
            <div class="content-card">
                <h3 class="card-title">
                    <i class="bi bi-diagram-3"></i>
                    خطوط الإنتاج النشطة
                </h3>
                <div class="recent-list">
                    {% for line in active_lines %}
                    <div class="recent-item">
                        <div class="recent-item-title">
                            <a href="{% url 'production:production_line_detail' line.pk %}" 
                               style="text-decoration: none; color: inherit;">
                                {{ line.name }}
                            </a>
                        </div>
                        <div class="recent-item-meta">
                            <span>{{ line.warehouse.name }}</span>
                            <span class="status-badge status-{{ line.status }}">
                                {{ line.get_status_display }}
                            </span>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-diagram-3" style="font-size: 2rem;"></i>
                        <p class="mt-2">لا توجد خطوط إنتاج نشطة</p>
                    </div>
                    {% endfor %}
                </div>
                {% if active_lines %}
                <div class="text-center mt-3">
                    <a href="{% url 'production:production_line_list' %}" class="btn btn-outline-primary btn-sm">
                        عرض الكل
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- قوائم المواد النشطة -->
            <div class="content-card">
                <h3 class="card-title">
                    <i class="bi bi-list-check"></i>
                    قوائم المواد النشطة
                </h3>
                <div class="recent-list">
                    {% for bom in active_bom_list %}
                    <div class="recent-item">
                        <div class="recent-item-title">
                            <a href="{% url 'production:bom_detail' bom.pk %}" 
                               style="text-decoration: none; color: inherit;">
                                {{ bom.name }}
                            </a>
                        </div>
                        <div class="recent-item-meta">
                            <span>{{ bom.product.name }}</span>
                            <span class="status-badge status-{{ bom.status }}">
                                v{{ bom.version }}
                            </span>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-list-check" style="font-size: 2rem;"></i>
                        <p class="mt-2">لا توجد قوائم مواد نشطة</p>
                    </div>
                    {% endfor %}
                </div>
                {% if active_bom_list %}
                <div class="text-center mt-3">
                    <a href="{% url 'production:bom_list' %}" class="btn btn-outline-primary btn-sm">
                        عرض الكل
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
