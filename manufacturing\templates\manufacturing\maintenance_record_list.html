{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .search-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .maintenance-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .maintenance-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .maintenance-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .maintenance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(52, 152, 219, 0.1);
    }

    .maintenance-title {
        font-size: 1.3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #3498db, #2980b9);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .maintenance-info {
        margin-bottom: 1rem;
    }

    .maintenance-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;
        padding: 0.5rem;
        background: rgba(52, 152, 219, 0.05);
        border-radius: 10px;
    }

    .maintenance-info-label {
        font-weight: 600;
        color: #495057;
    }

    .maintenance-info-value {
        font-weight: 500;
        color: #2c3e50;
    }

    .priority-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .priority-low { background: #27ae60; color: white; }
    .priority-normal { background: #3498db; color: white; }
    .priority-high { background: #f39c12; color: white; }
    .priority-urgent { background: #e74c3c; color: white; }

    .maintenance-type-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        background: #34495e;
        color: white;
    }

    .overdue-indicator {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: #e74c3c;
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .cost-display {
        font-size: 1.2rem;
        font-weight: 700;
        color: #3498db;
        text-align: center;
        padding: 1rem;
        background: rgba(52, 152, 219, 0.1);
        border-radius: 10px;
        margin: 1rem 0;
    }

    .btn-group-custom {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .btn-custom {
        flex: 1;
        padding: 0.8rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        text-align: center;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        margin: 2rem 0;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-box {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(52, 152, 219, 0.2);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #3498db;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-tools me-3"></i>
                    سجلات الصيانة
                </h1>
                <p class="text-white-50 mb-0 mt-2">تتبع ومراقبة جميع أعمال الصيانة والإصلاحات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:maintenance_record_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>سجل صيانة جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-row">
        <div class="stat-box">
            <div class="stat-number">{{ page_obj|length }}</div>
            <div class="stat-label">إجمالي السجلات</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">
                {% for record in page_obj %}
                    {% if record.status == 'in_progress' %}1{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">صيانة جارية</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">
                {% for record in page_obj %}
                    {% if record.is_overdue %}1{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">صيانة متأخرة</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">
                {% for record in page_obj %}
                    {% if record.total_cost %}{{ record.total_cost|add:0 }}{% endif %}
                {% empty %}0{% endfor %}
            </div>
            <div class="stat-label">إجمالي التكلفة</div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label fw-bold">نوع الصيانة</label>
                <select name="maintenance_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for type in maintenance_types %}
                    <option value="{{ type.id }}" {% if request.GET.maintenance_type == type.id|stringformat:"s" %}selected{% endif %}>
                        {{ type.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled" {% if request.GET.status == 'scheduled' %}selected{% endif %}>مجدولة</option>
                    <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                    <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتملة</option>
                    <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغية</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">المعدة</label>
                <select name="equipment" class="form-select">
                    <option value="">جميع المعدات</option>
                    {% for equipment in equipment_list %}
                    <option value="{{ equipment.id }}" {% if request.GET.equipment == equipment.id|stringformat:"s" %}selected{% endif %}>
                        {{ equipment.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>

    <!-- قائمة سجلات الصيانة -->
    {% if page_obj %}
    <div class="maintenance-grid">
        {% for record in page_obj %}
        <div class="maintenance-card">
            {% if record.is_overdue %}
            <div class="overdue-indicator">
                <i class="bi bi-clock me-1"></i>متأخرة
            </div>
            {% endif %}
            
            <div class="maintenance-header">
                <div class="maintenance-title">{{ record.title }}</div>
                <div>
                    <span class="maintenance-type-badge">{{ record.maintenance_type.name }}</span>
                </div>
            </div>

            <div class="maintenance-info">
                <div class="maintenance-info-item">
                    <span class="maintenance-info-label">المعدة:</span>
                    <span class="maintenance-info-value">{{ record.equipment.name }}</span>
                </div>
                <div class="maintenance-info-item">
                    <span class="maintenance-info-label">الحالة:</span>
                    <span class="maintenance-info-value">
                        {% status_badge record.status "maintenance" %}
                    </span>
                </div>
                <div class="maintenance-info-item">
                    <span class="maintenance-info-label">الأولوية:</span>
                    <span class="maintenance-info-value">
                        <span class="priority-badge priority-{{ record.priority }}">
                            {{ record.get_priority_display }}
                        </span>
                    </span>
                </div>
                <div class="maintenance-info-item">
                    <span class="maintenance-info-label">التاريخ المجدول:</span>
                    <span class="maintenance-info-value">{{ record.scheduled_date|date:"Y/m/d H:i" }}</span>
                </div>
                {% if record.assigned_technician %}
                <div class="maintenance-info-item">
                    <span class="maintenance-info-label">الفني المكلف:</span>
                    <span class="maintenance-info-value">{{ record.assigned_technician.username }}</span>
                </div>
                {% endif %}
                {% if record.actual_start_date %}
                <div class="maintenance-info-item">
                    <span class="maintenance-info-label">تاريخ البدء الفعلي:</span>
                    <span class="maintenance-info-value">{{ record.actual_start_date|date:"Y/m/d H:i" }}</span>
                </div>
                {% endif %}
            </div>

            {% if record.total_cost %}
            <div class="cost-display">
                <i class="bi bi-currency-dollar me-2"></i>
                التكلفة الإجمالية: {{ record.total_cost|format_currency }}
            </div>
            {% endif %}

            {% if record.description %}
            <div class="mt-2">
                <small class="text-muted">
                    <strong>الوصف:</strong> {{ record.description|truncatewords:15 }}
                </small>
            </div>
            {% endif %}

            <div class="btn-group-custom">
                <a href="#" class="btn-custom btn-primary-custom">
                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                </a>
                {% if record.status == 'scheduled' %}
                <a href="#" class="btn-custom btn-outline-success">
                    <i class="bi bi-play-circle me-2"></i>بدء الصيانة
                </a>
                {% elif record.status == 'in_progress' %}
                <a href="#" class="btn-custom btn-outline-primary">
                    <i class="bi bi-check-circle me-2"></i>إنهاء الصيانة
                </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    {% else %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="bi bi-tools" style="font-size: 4rem; color: #3498db;"></i>
        </div>
        <h3 style="color: #2c3e50;">لا توجد سجلات صيانة</h3>
        <p style="color: #6c757d;">لم يتم العثور على سجلات صيانة. ابدأ بإنشاء سجل صيانة جديد.</p>
        <a href="{% url 'manufacturing:maintenance_record_create' %}" class="btn btn-primary btn-lg">
            <i class="bi bi-plus-circle me-2"></i>إنشاء سجل صيانة جديد
        </a>
    </div>
    {% endif %}

    <!-- أزرار العودة -->
    <div class="text-center mt-4">
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                <i class="bi bi-house me-2"></i>لوحة التحكم
            </a>
            <a href="{% url 'manufacturing:maintenance_dashboard' %}" class="btn btn-outline-primary">
                <i class="bi bi-tools me-2"></i>لوحة تحكم الصيانة
            </a>
        </div>
    </div>
</div>
{% endblock %}
