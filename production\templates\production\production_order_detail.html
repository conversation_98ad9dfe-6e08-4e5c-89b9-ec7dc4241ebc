{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-weight: 700;
        text-transform: uppercase;
    }
    .status-draft { background: #6c757d; color: white; }
    .status-planned { background: #17a2b8; color: white; }
    .status-released { background: #ffc107; color: black; }
    .status-in_progress { background: #007bff; color: white; }
    .status-completed { background: #28a745; color: white; }
    .status-cancelled { background: #dc3545; color: white; }
    .status-on_hold { background: #fd7e14; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-clipboard-check me-3"></i>
                    {{ order.order_number }}
                </h1>
                <p class="text-white-50 mb-0 mt-2">
                    {{ order.bom.product.name }} - {{ order.production_line.name }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ order.status }}">
                    {{ order.get_status_display }}
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="content-card">
                <h3>معلومات الأمر</h3>
                <div class="row g-3">
                    <div class="col-md-6">
                        <strong>رقم الأمر:</strong> {{ order.order_number }}
                    </div>
                    <div class="col-md-6">
                        <strong>قائمة المواد:</strong> {{ order.bom.name }}
                    </div>
                    <div class="col-md-6">
                        <strong>المنتج:</strong> {{ order.bom.product.name }}
                    </div>
                    <div class="col-md-6">
                        <strong>خط الإنتاج:</strong> {{ order.production_line.name }}
                    </div>
                    <div class="col-md-6">
                        <strong>الكمية المطلوبة:</strong> {{ order.quantity_ordered }}
                    </div>
                    <div class="col-md-6">
                        <strong>الكمية المنتجة:</strong> {{ order.quantity_produced }}
                    </div>
                    <div class="col-md-6">
                        <strong>نسبة الإنجاز:</strong> 
                        <div class="progress mt-1" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ order.completion_percentage }}%">
                                {{ order.completion_percentage|floatformat:1 }}%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <strong>الأولوية:</strong> {{ order.get_priority_display }}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ البدء المخطط:</strong> {{ order.planned_start_date|date:"Y-m-d H:i" }}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الانتهاء المخطط:</strong> {{ order.planned_end_date|date:"Y-m-d H:i" }}
                    </div>
                    {% if order.actual_start_date %}
                    <div class="col-md-6">
                        <strong>تاريخ البدء الفعلي:</strong> {{ order.actual_start_date|date:"Y-m-d H:i" }}
                    </div>
                    {% endif %}
                    {% if order.actual_end_date %}
                    <div class="col-md-6">
                        <strong>تاريخ الانتهاء الفعلي:</strong> {{ order.actual_end_date|date:"Y-m-d H:i" }}
                    </div>
                    {% endif %}
                    {% if order.planner %}
                    <div class="col-md-6">
                        <strong>المخطط:</strong> {{ order.planner.get_full_name|default:order.planner.username }}
                    </div>
                    {% endif %}
                    {% if order.supervisor %}
                    <div class="col-md-6">
                        <strong>المشرف:</strong> {{ order.supervisor.get_full_name|default:order.supervisor.username }}
                    </div>
                    {% endif %}
                </div>
                
                {% if order.notes %}
                <div class="mt-3">
                    <strong>ملاحظات:</strong>
                    <p class="mt-2">{{ order.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="col-md-4">
            <div class="content-card">
                <h3>إجراءات سريعة</h3>
                <div class="d-grid gap-2">
                    {% if order.status == 'planned' or order.status == 'released' %}
                    <a href="{% url 'production:production_order_start' order.pk %}" 
                       class="btn btn-success">
                        <i class="bi bi-play-circle me-2"></i>بدء الإنتاج
                    </a>
                    {% endif %}
                    
                    {% if order.status == 'in_progress' %}
                    <a href="{% url 'production:production_order_complete' order.pk %}" 
                       class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>إكمال الإنتاج
                    </a>
                    {% endif %}
                    
                    {% if order.status not in 'completed,cancelled' %}
                    <a href="{% url 'production:production_order_cancel' order.pk %}" 
                       class="btn btn-warning"
                       onclick="return confirm('هل أنت متأكد من إلغاء الأمر؟')">
                        <i class="bi bi-x-circle me-2"></i>إلغاء الأمر
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'production:production_order_update' order.pk %}" 
                       class="btn btn-outline-primary">
                        <i class="bi bi-pencil me-2"></i>تعديل
                    </a>
                    
                    <a href="{% url 'production:production_order_delete' order.pk %}" 
                       class="btn btn-outline-danger"
                       onclick="return confirm('هل أنت متأكد من حذف الأمر؟')">
                        <i class="bi bi-trash me-2"></i>حذف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{% url 'production:production_order_list' %}" class="btn btn-secondary btn-lg">
            <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}
