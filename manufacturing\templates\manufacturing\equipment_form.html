{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin: 2rem 0;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #667eea;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .alert-custom {
        border: none;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-info-custom {
        background: rgba(13, 202, 240, 0.1);
        color: #0dcaf0;
        border-left: 4px solid #0dcaf0;
    }

    .form-text-custom {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    @media (max-width: 768px) {
        .form-container {
            padding: 2rem 1.5rem;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .btn-group-custom {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {% if equipment %}تحديث المعدة{% else %}إضافة معدة جديدة{% endif %}
                </h1>
                <p class="text-white-50 mb-0 mt-2">
                    {% if equipment %}تحديث بيانات المعدة - {{ equipment.name }}{% else %}إضافة معدة جديدة للنظام{% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:equipment_list' %}" class="btn btn-light">
                        <i class="bi bi-gear-wide-connected me-2"></i>المعدات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- النموذج -->
    <div class="form-container">
        <div class="alert alert-info-custom">
            <i class="bi bi-info-circle me-2"></i>
            <strong>ملاحظة:</strong> تأكد من إدخال جميع البيانات المطلوبة بدقة لضمان إدارة فعالة للمعدة.
        </div>

        <form method="post" id="equipmentForm">
            {% csrf_token %}
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">اسم المعدة</label>
                            {{ form.name }}
                            <div class="form-text-custom">اسم وصفي للمعدة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">كود المعدة</label>
                            {{ form.code }}
                            <div class="form-text-custom">كود فريد لتمييز المعدة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">فئة المعدة</label>
                            {{ form.category }}
                            <div class="form-text-custom">تصنيف المعدة حسب النوع</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">خط الإنتاج</label>
                            {{ form.production_line }}
                            <div class="form-text-custom">خط الإنتاج المخصص لهذه المعدة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الموقع</label>
                            {{ form.location }}
                            <div class="form-text-custom">موقع المعدة في المصنع</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">حالة النشاط</label>
                            <div class="form-check mt-2">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    المعدة نشطة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المواصفات الفنية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-gear"></i>
                    المواصفات الفنية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الشركة المصنعة</label>
                            {{ form.manufacturer }}
                            <div class="form-text-custom">اسم الشركة المصنعة للمعدة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الموديل</label>
                            {{ form.model }}
                            <div class="form-text-custom">موديل المعدة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الرقم التسلسلي</label>
                            {{ form.serial_number }}
                            <div class="form-text-custom">الرقم التسلسلي الفريد للمعدة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">سنة التصنيع</label>
                            {{ form.year_manufactured }}
                            <div class="form-text-custom">السنة التي تم تصنيع المعدة فيها</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">الطاقة المقدرة</label>
                            {{ form.rated_capacity }}
                            <div class="form-text-custom">الطاقة الإنتاجية المقدرة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">وحدة الطاقة</label>
                            {{ form.capacity_unit }}
                            <div class="form-text-custom">وحدة قياس الطاقة (قطعة/ساعة، كيلو/ساعة، إلخ)</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">استهلاك الطاقة (كيلو وات)</label>
                            {{ form.power_consumption }}
                            <div class="form-text-custom">استهلاك الطاقة الكهربائية</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الحالة والصيانة -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-tools"></i>
                    الحالة والصيانة
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">حالة التشغيل</label>
                            {{ form.status }}
                            <div class="form-text-custom">الحالة التشغيلية الحالية للمعدة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">الحالة الفنية</label>
                            {{ form.condition }}
                            <div class="form-text-custom">تقييم الحالة الفنية للمعدة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تاريخ التركيب</label>
                            {{ form.installation_date }}
                            <div class="form-text-custom">تاريخ تركيب المعدة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تاريخ آخر صيانة</label>
                            {{ form.last_maintenance_date }}
                            <div class="form-text-custom">تاريخ آخر صيانة تم إجراؤها</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تاريخ الصيانة القادمة</label>
                            {{ form.next_maintenance_date }}
                            <div class="form-text-custom">تاريخ الصيانة المجدولة القادمة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المعلومات المالية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-currency-dollar"></i>
                    المعلومات المالية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تكلفة الشراء</label>
                            {{ form.purchase_cost }}
                            <div class="form-text-custom">التكلفة الأصلية لشراء المعدة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">القيمة الحالية</label>
                            {{ form.current_value }}
                            <div class="form-text-custom">القيمة السوقية الحالية للمعدة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-file-text"></i>
                    ملاحظات إضافية
                </h3>
                
                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    {{ form.notes }}
                    <div class="form-text-custom">أي ملاحظات أو معلومات إضافية عن المعدة</div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-3 justify-content-between align-items-center">
                <!-- أزرار العودة -->
                <div class="d-flex gap-2">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-2"></i>لوحة التحكم
                    </a>
                    <a href="{% url 'manufacturing:equipment_list' %}" class="btn btn-outline-primary">
                        <i class="bi bi-gear-wide-connected me-2"></i>قائمة المعدات
                    </a>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="d-flex gap-3">
                    <a href="{% url 'manufacturing:equipment_list' %}" class="btn-secondary-custom">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                    <button type="submit" class="btn-primary-custom">
                        <i class="bi bi-check-circle"></i>
                        {% if equipment %}تحديث المعدة{% else %}إضافة المعدة{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.getElementById('equipmentForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    // تنسيق حقول التاريخ
    const dateInputs = form.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        if (!input.value && input.name === 'installation_date') {
            const today = new Date();
            input.value = today.toISOString().split('T')[0];
        }
    });

    // إنشاء كود تلقائي بناءً على اسم المعدة
    const nameInput = form.querySelector('input[name="name"]');
    const codeInput = form.querySelector('input[name="code"]');
    
    if (nameInput && codeInput && !codeInput.value) {
        nameInput.addEventListener('blur', function() {
            if (this.value && !codeInput.value) {
                const code = 'EQ-' + this.value.substring(0, 3).toUpperCase() + '-' + 
                           Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                codeInput.value = code;
            }
        });
    }
});
</script>
{% endblock %}
