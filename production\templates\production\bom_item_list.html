{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-ul me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'production:bom_item_create' %}" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>إنشاء جديد
                </a>
            </div>
        </div>
    </div>

    <div class="content-card">
        <div class="text-center py-5">
            <i class="bi bi-list-ul" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3">عناصر قوائم المواد</h3>
            <p class="text-muted">سيتم تطوير هذه الصفحة قريباً</p>
            <a href="{% url 'production:bom_list' %}" class="btn btn-primary">
                <i class="bi bi-arrow-left me-2"></i>العودة لقوائم المواد
            </a>
        </div>
    </div>
</div>
{% endblock %}
