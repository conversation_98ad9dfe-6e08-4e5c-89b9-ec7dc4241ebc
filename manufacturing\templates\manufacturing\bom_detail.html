{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .detail-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #667eea;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .info-item {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 10px;
        padding: 1rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .info-value {
        font-weight: 500;
        color: #2c3e50;
        font-size: 1.1rem;
    }

    .table-modern {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .table-modern thead {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .table-modern th {
        border: none;
        padding: 1rem;
        font-weight: 700;
        text-transform: uppercase;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }

    .table-modern td {
        border: none;
        padding: 1rem;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .table-modern tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .cost-summary {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        border-radius: 15px;
        padding: 2rem;
        margin-top: 2rem;
    }

    .cost-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.8rem 0;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    .cost-item:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.2rem;
        color: #2c3e50;
    }

    .cost-label {
        font-weight: 600;
        color: #495057;
    }

    .cost-value {
        font-weight: 700;
        color: #2c3e50;
    }

    .btn-action {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 0.8rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-secondary-action {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-secondary-action:hover {
        background: #6c757d;
        color: white;
    }

    .empty-items {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .empty-items i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #667eea;
    }

    @media (max-width: 768px) {
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .btn-group {
            flex-direction: column;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-check me-3"></i>
                    {{ bom.name }}
                </h1>
                <p class="text-white-50 mb-0 mt-2">تفاصيل قائمة المواد - {{ bom.code }}</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <a href="{% url 'manufacturing:bom_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-right me-2"></i>العودة للقائمة
                    </a>
                    <a href="#" class="btn btn-light">
                        <i class="bi bi-pencil me-2"></i>تعديل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات أساسية -->
    <div class="detail-section">
        <h3 class="section-title">
            <i class="bi bi-info-circle"></i>
            المعلومات الأساسية
        </h3>
        
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">اسم قائمة المواد</div>
                <div class="info-value">{{ bom.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الكود</div>
                <div class="info-value">{{ bom.code }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">المنتج النهائي</div>
                <div class="info-value">{{ bom.product.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الإصدار</div>
                <div class="info-value">{{ bom.version }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الكمية المنتجة</div>
                <div class="info-value">{{ bom.quantity_produced|format_quantity }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">وقت الإنتاج</div>
                <div class="info-value">{{ bom.production_time_minutes|time_format }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">الحالة</div>
                <div class="info-value">
                    {% status_badge bom.status "bom" %}
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">تاريخ السريان</div>
                <div class="info-value">{{ bom.effective_date|date:"Y/m/d" }}</div>
            </div>
        </div>

        {% if bom.notes %}
        <div class="mt-3">
            <div class="info-label">ملاحظات</div>
            <div class="info-value">{{ bom.notes }}</div>
        </div>
        {% endif %}
    </div>

    <!-- عناصر قائمة المواد -->
    <div class="detail-section">
        <h3 class="section-title">
            <i class="bi bi-list-ul"></i>
            عناصر قائمة المواد
        </h3>
        
        {% if bom_items %}
        <div class="table-modern">
            <table class="table">
                <thead>
                    <tr>
                        <th>التسلسل</th>
                        <th>المادة</th>
                        <th>النوع</th>
                        <th>الكمية المطلوبة</th>
                        <th>تكلفة الوحدة</th>
                        <th>نسبة الفاقد</th>
                        <th>الإجمالي</th>
                        <th>حرجة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in bom_items %}
                    <tr>
                        <td><strong>{{ item.sequence }}</strong></td>
                        <td>{{ item.material.name }}</td>
                        <td>
                            <span class="badge bg-info">{{ item.get_item_type_display }}</span>
                        </td>
                        <td>{{ item.quantity_required|format_quantity }}</td>
                        <td>{{ item.unit_cost|format_currency }}</td>
                        <td>{{ item.waste_percentage }}%</td>
                        <td><strong>{{ item.total_cost|format_currency }}</strong></td>
                        <td>
                            {% if item.is_critical %}
                                <span class="badge bg-warning">حرجة</span>
                            {% else %}
                                <span class="badge bg-secondary">عادية</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-items">
            <i class="bi bi-inbox"></i>
            <h5>لا توجد عناصر في قائمة المواد</h5>
            <p>لم يتم إضافة أي مواد أو مكونات لهذه القائمة بعد.</p>
            <a href="#" class="btn-action">
                <i class="bi bi-plus-circle"></i>إضافة عنصر جديد
            </a>
        </div>
        {% endif %}
    </div>

    <!-- ملخص التكاليف -->
    <div class="detail-section">
        <h3 class="section-title">
            <i class="bi bi-calculator"></i>
            ملخص التكاليف
        </h3>
        
        <div class="cost-summary">
            <div class="cost-item">
                <span class="cost-label">تكلفة المواد:</span>
                <span class="cost-value">{{ bom.total_material_cost|format_currency }}</span>
            </div>
            <div class="cost-item">
                <span class="cost-label">تكلفة العمالة:</span>
                <span class="cost-value">{{ bom.labor_cost|format_currency }}</span>
            </div>
            <div class="cost-item">
                <span class="cost-label">التكاليف الإضافية:</span>
                <span class="cost-value">{{ bom.overhead_cost|format_currency }}</span>
            </div>
            <div class="cost-item">
                <span class="cost-label">إجمالي التكلفة:</span>
                <span class="cost-value">{{ bom.total_cost|format_currency }}</span>
            </div>
            <div class="cost-item">
                <span class="cost-label">تكلفة الوحدة:</span>
                <span class="cost-value">{{ bom.cost_per_unit|format_currency }}</span>
            </div>
        </div>
    </div>

    <!-- إحصائيات الاستخدام -->
    <div class="detail-section">
        <h3 class="section-title">
            <i class="bi bi-graph-up"></i>
            إحصائيات الاستخدام
        </h3>
        
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">عدد أوامر الإنتاج</div>
                <div class="info-value">{{ orders_count|default:0 }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">تاريخ الإنشاء</div>
                <div class="info-value">{{ bom.created_at|date:"Y/m/d H:i" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">آخر تحديث</div>
                <div class="info-value">{{ bom.updated_at|date:"Y/m/d H:i" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">أنشئ بواسطة</div>
                <div class="info-value">{{ bom.created_by.username|default:"غير محدد" }}</div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="text-center">
        <a href="#" class="btn-action">
            <i class="bi bi-plus-circle"></i>إضافة عنصر جديد
        </a>
        <a href="#" class="btn-action">
            <i class="bi bi-clipboard-data"></i>إنشاء أمر إنتاج
        </a>
        <a href="#" class="btn-secondary-action">
            <i class="bi bi-printer"></i>طباعة
        </a>
        <a href="#" class="btn-secondary-action">
            <i class="bi bi-download"></i>تصدير
        </a>
    </div>
</div>
{% endblock %}
