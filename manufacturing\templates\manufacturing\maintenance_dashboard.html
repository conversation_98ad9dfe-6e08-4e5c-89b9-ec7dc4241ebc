{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
        font-size: 1rem;
    }

    .data-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #667eea;
    }

    .table-modern {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .table-modern thead {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .table-modern th {
        border: none;
        padding: 1rem;
        font-weight: 700;
        text-transform: uppercase;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }

    .table-modern td {
        border: none;
        padding: 1rem;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .table-modern tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .equipment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .equipment-card {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        padding: 1.5rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .equipment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .equipment-name {
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .equipment-info {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .maintenance-date {
        color: #e74c3c;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .overdue {
        background: rgba(231, 76, 60, 0.1);
        border-color: #e74c3c;
    }

    .due-soon {
        background: rgba(243, 156, 18, 0.1);
        border-color: #f39c12;
    }

    .btn-action {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 0.8rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-tools me-3"></i>
                    لوحة تحكم الصيانة
                </h1>
                <p class="text-white-50 mb-0 mt-2">إدارة ومتابعة جميع أعمال الصيانة والجدولة</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:maintenance_record_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>سجل صيانة جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الصيانة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon" style="background: var(--danger-gradient);">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <div class="stat-number">{{ overdue_maintenance }}</div>
            <div class="stat-label">صيانة متأخرة</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: var(--warning-gradient);">
                <i class="bi bi-clock"></i>
            </div>
            <div class="stat-number">{{ due_this_week }}</div>
            <div class="stat-label">مستحقة هذا الأسبوع</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: var(--info-gradient);">
                <i class="bi bi-gear-fill"></i>
            </div>
            <div class="stat-number">{{ active_maintenance }}</div>
            <div class="stat-label">صيانة نشطة</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: var(--success-gradient);">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-number">{{ completed_this_month }}</div>
            <div class="stat-label">مكتملة هذا الشهر</div>
        </div>
    </div>

    <!-- المعدات التي تحتاج صيانة -->
    {% if equipment_needing_maintenance %}
    <div class="data-section">
        <h3 class="section-title">
            <i class="bi bi-exclamation-triangle"></i>
            معدات تحتاج صيانة
        </h3>
        
        <div class="equipment-grid">
            {% for equipment in equipment_needing_maintenance %}
            <div class="equipment-card {% if equipment.needs_maintenance %}overdue{% else %}due-soon{% endif %}">
                <div class="equipment-name">{{ equipment.name }}</div>
                <div class="equipment-info">{{ equipment.production_line.name }}</div>
                <div class="equipment-info">الكود: {{ equipment.code }}</div>
                <div class="maintenance-date">
                    الصيانة القادمة: {{ equipment.next_maintenance_date|date:"Y/m/d" }}
                    {% if equipment.days_until_maintenance < 0 %}
                    (متأخرة {{ equipment.days_until_maintenance|abs }} يوم)
                    {% elif equipment.days_until_maintenance == 0 %}
                    (اليوم)
                    {% else %}
                    (خلال {{ equipment.days_until_maintenance }} يوم)
                    {% endif %}
                </div>
                <div class="mt-2">
                    <a href="{% url 'manufacturing:equipment_detail' equipment.pk %}" 
                       class="btn btn-sm btn-outline-primary">
                        عرض التفاصيل
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- أحدث سجلات الصيانة -->
    {% if recent_maintenance %}
    <div class="data-section">
        <h3 class="section-title">
            <i class="bi bi-clock-history"></i>
            أحدث سجلات الصيانة
        </h3>
        
        <div class="table-modern">
            <table class="table">
                <thead>
                    <tr>
                        <th>المعدة</th>
                        <th>نوع الصيانة</th>
                        <th>الحالة</th>
                        <th>الأولوية</th>
                        <th>التاريخ المجدول</th>
                        <th>الفني المكلف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in recent_maintenance %}
                    <tr>
                        <td><strong>{{ record.equipment.name }}</strong></td>
                        <td>{{ record.maintenance_type.name }}</td>
                        <td>
                            {% status_badge record.status "maintenance" %}
                        </td>
                        <td>
                            {% priority_badge record.priority %}
                        </td>
                        <td>{{ record.scheduled_date|date:"Y/m/d H:i" }}</td>
                        <td>{{ record.assigned_technician.username|default:"غير محدد" }}</td>
                        <td>
                            <a href="#" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="text-center mt-3">
            <a href="{% url 'manufacturing:maintenance_record_list' %}" class="btn btn-primary btn-lg">
                <i class="bi bi-list-ul me-2"></i>عرض جميع سجلات الصيانة
            </a>
        </div>
    </div>
    {% endif %}

    <!-- روابط سريعة -->
    <div class="data-section">
        <h3 class="section-title">
            <i class="bi bi-lightning-charge"></i>
            إجراءات سريعة
        </h3>
        
        <div class="row">
            <div class="col-md-3">
                <a href="{% url 'manufacturing:maintenance_record_create' %}" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-plus-circle"></i>سجل صيانة جديد
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'manufacturing:maintenance_schedule_list' %}" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-calendar-check"></i>جدولة الصيانة
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'manufacturing:equipment_list' %}" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-gear-wide-connected"></i>إدارة المعدات
                </a>
            </div>
            <div class="col-md-3">
                <a href="#" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-graph-up"></i>تقارير الصيانة
                </a>
            </div>
        </div>
    </div>

    <!-- أزرار العودة -->
    <div class="text-center mt-4">
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                <i class="bi bi-house me-2"></i>لوحة التحكم الرئيسية
            </a>
            <a href="{% url 'manufacturing:equipment_list' %}" class="btn btn-outline-primary">
                <i class="bi bi-gear-wide-connected me-2"></i>إدارة المعدات
            </a>
        </div>
    </div>
</div>
{% endblock %}
