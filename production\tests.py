from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from decimal import Decimal
from datetime import date, timedelta
from django.utils import timezone

from .models import (
    ProductionLine, WorkCenter, BillOfMaterials, BOMItem, ProductionOrder
)
from definitions.models import WarehouseDefinition, ProductDefinition, PersonDefinition


class ProductionLineTestCase(TestCase):
    """اختبارات خطوط الإنتاج"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )

        # إنشاء مخزن للاختبار
        self.warehouse = WarehouseDefinition.objects.create(
            name='مخزن الاختبار',
            code='TEST_WH',
            created_by=self.user
        )

        # إنشاء خط إنتاج للاختبار
        self.production_line = ProductionLine.objects.create(
            name='خط إنتاج الاختبار',
            code='TEST_LINE',
            warehouse=self.warehouse,
            capacity_per_hour=Decimal('100.00'),
            efficiency_rate=Decimal('85.00'),
            created_by=self.user
        )

    def test_production_line_creation(self):
        """اختبار إنشاء خط الإنتاج"""
        self.assertEqual(self.production_line.name, 'خط إنتاج الاختبار')
        self.assertEqual(self.production_line.code, 'TEST_LINE')
        self.assertEqual(self.production_line.warehouse, self.warehouse)
        self.assertEqual(self.production_line.capacity_per_hour, Decimal('100.00'))
        self.assertEqual(self.production_line.efficiency_rate, Decimal('85.00'))
        self.assertTrue(self.production_line.is_active)

    def test_production_line_str(self):
        """اختبار تمثيل خط الإنتاج كنص"""
        expected = f"{self.production_line.name} ({self.production_line.code})"
        self.assertEqual(str(self.production_line), expected)


class BillOfMaterialsTestCase(TestCase):
    """اختبارات قوائم المواد"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )

        # إنشاء فئة منتج للاختبار
        from definitions.models import ProductCategory
        self.category = ProductCategory.objects.create(
            name='فئة الاختبار',
            code='TEST_CAT',
            created_by=self.user
        )

        # إنشاء منتج للاختبار
        self.product = ProductDefinition.objects.create(
            name='منتج الاختبار',
            code='TEST_PROD',
            category=self.category,
            created_by=self.user
        )

        # إنشاء قائمة مواد للاختبار
        self.bom = BillOfMaterials.objects.create(
            name='قائمة مواد الاختبار',
            code='TEST_BOM',
            product=self.product,
            version='1.0',
            quantity_produced=Decimal('1.000'),
            production_time_minutes=60,
            labor_cost=Decimal('50.00'),
            overhead_cost=Decimal('25.00'),
            created_by=self.user
        )

    def test_bom_creation(self):
        """اختبار إنشاء قائمة المواد"""
        self.assertEqual(self.bom.name, 'قائمة مواد الاختبار')
        self.assertEqual(self.bom.code, 'TEST_BOM')
        self.assertEqual(self.bom.product, self.product)
        self.assertEqual(self.bom.version, '1.0')
        self.assertEqual(self.bom.quantity_produced, Decimal('1.000'))
        self.assertEqual(self.bom.production_time_minutes, 60)
        self.assertEqual(self.bom.labor_cost, Decimal('50.00'))
        self.assertEqual(self.bom.overhead_cost, Decimal('25.00'))

    def test_bom_total_cost(self):
        """اختبار حساب التكلفة الإجمالية"""
        # إضافة عنصر لقائمة المواد
        material = ProductDefinition.objects.create(
            name='مادة خام',
            code='RAW_MAT',
            category=self.category,
            created_by=self.user
        )

        BOMItem.objects.create(
            bom=self.bom,
            material=material,
            sequence=1,
            quantity_required=Decimal('2.000'),
            unit_cost=Decimal('10.00'),
            created_by=self.user
        )

        # التكلفة الإجمالية = تكلفة المواد + تكلفة العمالة + التكاليف الإضافية
        # تكلفة المواد = 2 * 10 = 20
        # إجمالي = 20 + 50 + 25 = 95
        expected_total = Decimal('95.00')
        self.assertEqual(self.bom.total_cost, expected_total)


class ProductionOrderTestCase(TestCase):
    """اختبارات أوامر الإنتاج"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )

        # إنشاء البيانات المطلوبة
        self.warehouse = WarehouseDefinition.objects.create(
            name='مخزن الاختبار',
            code='TEST_WH',
            created_by=self.user
        )

        self.production_line = ProductionLine.objects.create(
            name='خط إنتاج الاختبار',
            code='TEST_LINE',
            warehouse=self.warehouse,
            capacity_per_hour=Decimal('100.00'),
            created_by=self.user
        )

        # إنشاء فئة منتج للاختبار
        from definitions.models import ProductCategory
        self.category = ProductCategory.objects.create(
            name='فئة الاختبار',
            code='TEST_CAT',
            created_by=self.user
        )

        self.product = ProductDefinition.objects.create(
            name='منتج الاختبار',
            code='TEST_PROD',
            category=self.category,
            created_by=self.user
        )

        self.bom = BillOfMaterials.objects.create(
            name='قائمة مواد الاختبار',
            code='TEST_BOM',
            product=self.product,
            version='1.0',
            quantity_produced=Decimal('1.000'),
            production_time_minutes=60,
            status='active',
            created_by=self.user
        )

        # إنشاء أمر إنتاج
        self.production_order = ProductionOrder.objects.create(
            bom=self.bom,
            production_line=self.production_line,
            quantity_ordered=Decimal('10.000'),
            planned_start_date=timezone.now(),
            planned_end_date=timezone.now() + timedelta(days=1),
            created_by=self.user
        )

    def test_production_order_creation(self):
        """اختبار إنشاء أمر الإنتاج"""
        self.assertEqual(self.production_order.bom, self.bom)
        self.assertEqual(self.production_order.production_line, self.production_line)
        self.assertEqual(self.production_order.quantity_ordered, Decimal('10.000'))
        self.assertEqual(self.production_order.quantity_produced, Decimal('0'))
        self.assertEqual(self.production_order.status, 'draft')
        self.assertTrue(self.production_order.order_number)  # يجب أن يتم إنشاء رقم تلقائي

    def test_completion_percentage(self):
        """اختبار حساب نسبة الإنجاز"""
        # في البداية نسبة الإنجاز = 0%
        self.assertEqual(self.production_order.completion_percentage, 0)

        # بعد إنتاج 5 وحدات من أصل 10
        self.production_order.quantity_produced = Decimal('5.000')
        self.production_order.save()
        self.assertEqual(self.production_order.completion_percentage, 50)

        # بعد إنتاج 10 وحدات (مكتمل)
        self.production_order.quantity_produced = Decimal('10.000')
        self.production_order.save()
        self.assertEqual(self.production_order.completion_percentage, 100)


class ProductionViewsTestCase(TestCase):
    """اختبارات واجهات الإنتاج"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')

    def test_production_dashboard_view(self):
        """اختبار عرض لوحة تحكم الإنتاج"""
        response = self.client.get(reverse('production:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'إدارة الإنتاج')

    def test_production_line_list_view(self):
        """اختبار عرض قائمة خطوط الإنتاج"""
        response = self.client.get(reverse('production:production_line_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'خطوط الإنتاج')

    def test_bom_list_view(self):
        """اختبار عرض قائمة قوائم المواد"""
        response = self.client.get(reverse('production:bom_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'قوائم المواد')

    def test_production_order_list_view(self):
        """اختبار عرض قائمة أوامر الإنتاج"""
        response = self.client.get(reverse('production:production_order_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'أوامر الإنتاج')
