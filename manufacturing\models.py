from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
from definitions.models import ProductDefinition, WarehouseDefinition


class ProductionLine(models.Model):
    """خط الإنتاج"""
    PRODUCTION_LINE_STATUS = [
        ('active', 'نشط'),
        ('maintenance', 'صيانة'),
        ('inactive', 'غير نشط'),
        ('breakdown', 'عطل'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم خط الإنتاج")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود خط الإنتاج")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    capacity_per_hour = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الطاقة الإنتاجية/ساعة")
    efficiency_rate = models.DecimalField(max_digits=5, decimal_places=2, default=100.00,
                                        validators=[MinValueValidator(0), MaxValueValidator(100)],
                                        verbose_name="معدل الكفاءة %")
    status = models.CharField(max_length=20, choices=PRODUCTION_LINE_STATUS, default='active', verbose_name="الحالة")
    installation_date = models.DateField(verbose_name="تاريخ التركيب")
    last_maintenance = models.DateField(blank=True, null=True, verbose_name="آخر صيانة")
    next_maintenance = models.DateField(blank=True, null=True, verbose_name="الصيانة القادمة")
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                 related_name='supervised_lines', verbose_name="المشرف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_production_lines', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "خط إنتاج"
        verbose_name_plural = "خطوط الإنتاج"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def current_efficiency(self):
        """حساب الكفاءة الحالية"""
        if self.status == 'breakdown':
            return 0
        elif self.status == 'maintenance':
            return 0
        elif self.status == 'inactive':
            return 0
        return self.efficiency_rate

    @property
    def needs_maintenance(self):
        """فحص إذا كان يحتاج صيانة"""
        if self.next_maintenance:
            return timezone.now().date() >= self.next_maintenance
        return False


class BillOfMaterials(models.Model):
    """قائمة المواد (BOM)"""
    BOM_STATUS = [
        ('draft', 'مسودة'),
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('archived', 'مؤرشف'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم قائمة المواد")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود قائمة المواد")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE,
                              related_name='bom_as_product', verbose_name="المنتج النهائي")
    version = models.CharField(max_length=20, default="1.0", verbose_name="الإصدار")
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3, default=1,
                                          validators=[MinValueValidator(0.001)],
                                          verbose_name="الكمية المنتجة")
    production_time_minutes = models.IntegerField(validators=[MinValueValidator(1)],
                                                verbose_name="وقت الإنتاج (دقيقة)")
    labor_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0,
                                   validators=[MinValueValidator(0)], verbose_name="تكلفة العمالة")
    overhead_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0,
                                      validators=[MinValueValidator(0)], verbose_name="التكاليف الإضافية")
    status = models.CharField(max_length=20, choices=BOM_STATUS, default='draft', verbose_name="الحالة")
    effective_date = models.DateField(verbose_name="تاريخ السريان")
    expiry_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الانتهاء")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_boms', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "قائمة مواد"
        verbose_name_plural = "قوائم المواد"
        ordering = ['-created_at']
        unique_together = ['product', 'version']

    def __str__(self):
        return f"{self.name} - {self.product.name} (v{self.version})"

    @property
    def total_material_cost(self):
        """إجمالي تكلفة المواد"""
        return sum(item.total_cost for item in self.bom_items.all())

    @property
    def total_cost(self):
        """إجمالي التكلفة"""
        return self.total_material_cost + self.labor_cost + self.overhead_cost

    @property
    def cost_per_unit(self):
        """تكلفة الوحدة"""
        if self.quantity_produced > 0:
            return self.total_cost / self.quantity_produced
        return 0


class BOMItem(models.Model):
    """عنصر في قائمة المواد"""
    ITEM_TYPE = [
        ('raw_material', 'مادة خام'),
        ('component', 'مكون'),
        ('sub_assembly', 'تجميع فرعي'),
    ]

    bom = models.ForeignKey(BillOfMaterials, on_delete=models.CASCADE,
                          related_name='bom_items', verbose_name="قائمة المواد")
    material = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE,
                               related_name='bom_as_material', verbose_name="المادة")
    item_type = models.CharField(max_length=20, choices=ITEM_TYPE, default='raw_material',
                               verbose_name="نوع العنصر")
    quantity_required = models.DecimalField(max_digits=10, decimal_places=3,
                                          validators=[MinValueValidator(0.001)],
                                          verbose_name="الكمية المطلوبة")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2,
                                  validators=[MinValueValidator(0)], verbose_name="تكلفة الوحدة")
    waste_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0,
                                         validators=[MinValueValidator(0), MaxValueValidator(100)],
                                         verbose_name="نسبة الفاقد %")
    is_critical = models.BooleanField(default=False, verbose_name="مادة حرجة")
    supplier_lead_time = models.IntegerField(default=0, verbose_name="مدة التوريد (أيام)")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    sequence = models.IntegerField(default=1, verbose_name="التسلسل")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "عنصر قائمة مواد"
        verbose_name_plural = "عناصر قوائم المواد"
        ordering = ['sequence', 'material__name']
        unique_together = ['bom', 'material']

    def __str__(self):
        return f"{self.material.name} - {self.quantity_required}"

    @property
    def total_cost(self):
        """إجمالي التكلفة مع الفاقد"""
        base_cost = self.quantity_required * self.unit_cost
        waste_cost = base_cost * (self.waste_percentage / 100)
        return base_cost + waste_cost

    @property
    def quantity_with_waste(self):
        """الكمية مع الفاقد"""
        return self.quantity_required * (1 + self.waste_percentage / 100)


class ProductionOrder(models.Model):
    """أمر إنتاج"""
    ORDER_STATUS = [
        ('draft', 'مسودة'),
        ('planned', 'مخطط'),
        ('released', 'محرر'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
        ('on_hold', 'معلق'),
    ]

    PRIORITY = [
        ('low', 'منخفض'),
        ('normal', 'عادي'),
        ('high', 'عالي'),
        ('urgent', 'عاجل'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الأمر")
    bom = models.ForeignKey(BillOfMaterials, on_delete=models.CASCADE, verbose_name="قائمة المواد")
    production_line = models.ForeignKey(ProductionLine, on_delete=models.CASCADE, verbose_name="خط الإنتاج")
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=3,
                                         validators=[MinValueValidator(0.001)],
                                         verbose_name="الكمية المطلوبة")
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المنتجة")
    quantity_scrapped = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية التالفة")
    status = models.CharField(max_length=20, choices=ORDER_STATUS, default='draft', verbose_name="الحالة")
    priority = models.CharField(max_length=10, choices=PRIORITY, default='normal', verbose_name="الأولوية")
    planned_start_date = models.DateTimeField(verbose_name="تاريخ البدء المخطط")
    planned_end_date = models.DateTimeField(verbose_name="تاريخ الانتهاء المخطط")
    actual_start_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ البدء الفعلي")
    actual_end_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ الانتهاء الفعلي")
    customer_order_ref = models.CharField(max_length=100, blank=True, null=True,
                                        verbose_name="مرجع طلب العميل")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_production_orders', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "أمر إنتاج"
        verbose_name_plural = "أوامر الإنتاج"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.bom.product.name}"

    @property
    def completion_percentage(self):
        """نسبة الإنجاز"""
        if self.quantity_ordered > 0:
            return min((self.quantity_produced / self.quantity_ordered) * 100, 100)
        return 0

    @property
    def is_overdue(self):
        """فحص إذا كان متأخر"""
        if self.planned_end_date and self.status not in ['completed', 'cancelled']:
            return timezone.now() > self.planned_end_date
        return False

    @property
    def estimated_cost(self):
        """التكلفة المقدرة"""
        return self.bom.cost_per_unit * self.quantity_ordered


class ProductionOrderItem(models.Model):
    """عنصر أمر الإنتاج"""
    CONSUMPTION_STATUS = [
        ('planned', 'مخطط'),
        ('reserved', 'محجوز'),
        ('consumed', 'مستهلك'),
        ('returned', 'مرتجع'),
    ]

    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE,
                                       related_name='order_items', verbose_name="أمر الإنتاج")
    bom_item = models.ForeignKey(BOMItem, on_delete=models.CASCADE, verbose_name="عنصر قائمة المواد")
    quantity_required = models.DecimalField(max_digits=10, decimal_places=3,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المطلوبة")
    quantity_consumed = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المستهلكة")
    quantity_returned = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المرتجعة")
    status = models.CharField(max_length=20, choices=CONSUMPTION_STATUS, default='planned',
                            verbose_name="حالة الاستهلاك")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE,
                                verbose_name="المخزن")
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="رقم الدفعة")
    expiry_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الانتهاء")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "عنصر أمر إنتاج"
        verbose_name_plural = "عناصر أوامر الإنتاج"
        ordering = ['bom_item__sequence']

    def __str__(self):
        return f"{self.production_order.order_number} - {self.bom_item.material.name}"

    @property
    def consumption_percentage(self):
        """نسبة الاستهلاك"""
        if self.quantity_required > 0:
            return (self.quantity_consumed / self.quantity_required) * 100
        return 0


class QualityCheck(models.Model):
    """فحص الجودة"""
    CHECK_STATUS = [
        ('pending', 'معلق'),
        ('in_progress', 'قيد الفحص'),
        ('passed', 'نجح'),
        ('failed', 'فشل'),
        ('conditional', 'مشروط'),
    ]

    CHECK_TYPE = [
        ('incoming', 'فحص المواد الواردة'),
        ('in_process', 'فحص أثناء الإنتاج'),
        ('final', 'فحص نهائي'),
        ('random', 'فحص عشوائي'),
    ]

    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE,
                                       related_name='quality_checks', verbose_name="أمر الإنتاج")
    check_type = models.CharField(max_length=20, choices=CHECK_TYPE, verbose_name="نوع الفحص")
    check_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الفحص")
    inspector = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                related_name='quality_inspections', verbose_name="المفتش")
    quantity_checked = models.DecimalField(max_digits=10, decimal_places=3,
                                         validators=[MinValueValidator(0.001)],
                                         verbose_name="الكمية المفحوصة")
    quantity_passed = models.DecimalField(max_digits=10, decimal_places=3,
                                        validators=[MinValueValidator(0)],
                                        verbose_name="الكمية الناجحة")
    quantity_failed = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                        validators=[MinValueValidator(0)],
                                        verbose_name="الكمية الفاشلة")
    status = models.CharField(max_length=20, choices=CHECK_STATUS, default='pending',
                            verbose_name="حالة الفحص")
    defect_description = models.TextField(blank=True, null=True, verbose_name="وصف العيوب")
    corrective_action = models.TextField(blank=True, null=True, verbose_name="الإجراء التصحيحي")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "فحص جودة"
        verbose_name_plural = "فحوصات الجودة"
        ordering = ['-check_date']

    def __str__(self):
        return f"{self.production_order.order_number} - {self.get_check_type_display()}"

    @property
    def pass_rate(self):
        """معدل النجاح"""
        if self.quantity_checked > 0:
            return (self.quantity_passed / self.quantity_checked) * 100
        return 0


class ProductionReport(models.Model):
    """تقرير الإنتاج"""
    SHIFT = [
        ('morning', 'الصباحية'),
        ('evening', 'المسائية'),
        ('night', 'الليلية'),
    ]

    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE,
                                       related_name='production_reports', verbose_name="أمر الإنتاج")
    report_date = models.DateField(verbose_name="تاريخ التقرير")
    shift = models.CharField(max_length=10, choices=SHIFT, verbose_name="الوردية")
    operator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                               related_name='production_reports', verbose_name="المشغل")
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المنتجة")
    quantity_scrapped = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية التالفة")
    downtime_minutes = models.IntegerField(default=0, validators=[MinValueValidator(0)],
                                         verbose_name="وقت التوقف (دقيقة)")
    downtime_reason = models.TextField(blank=True, null=True, verbose_name="سبب التوقف")
    efficiency_rate = models.DecimalField(max_digits=5, decimal_places=2,
                                        validators=[MinValueValidator(0), MaxValueValidator(100)],
                                        verbose_name="معدل الكفاءة %")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_production_reports', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تقرير إنتاج"
        verbose_name_plural = "تقارير الإنتاج"
        ordering = ['-report_date', '-shift']
        unique_together = ['production_order', 'report_date', 'shift']

    def __str__(self):
        return f"{self.production_order.order_number} - {self.report_date} ({self.get_shift_display()})"

    @property
    def scrap_rate(self):
        """معدل التلف"""
        total_produced = self.quantity_produced + self.quantity_scrapped
        if total_produced > 0:
            return (self.quantity_scrapped / total_produced) * 100
        return 0


# نماذج إدارة المعدات والآلات
class EquipmentCategory(models.Model):
    """فئات المعدات"""
    name = models.CharField(max_length=100, verbose_name="اسم الفئة")
    code = models.CharField(max_length=20, unique=True, verbose_name="كود الفئة")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "فئة المعدات"
        verbose_name_plural = "فئات المعدات"
        ordering = ['name']

    def __str__(self):
        return self.name


class Equipment(models.Model):
    """المعدات والآلات"""
    EQUIPMENT_STATUS_CHOICES = [
        ('operational', 'تشغيلي'),
        ('maintenance', 'تحت الصيانة'),
        ('breakdown', 'معطل'),
        ('idle', 'متوقف'),
        ('retired', 'خارج الخدمة'),
    ]

    CONDITION_CHOICES = [
        ('excellent', 'ممتاز'),
        ('good', 'جيد'),
        ('fair', 'مقبول'),
        ('poor', 'ضعيف'),
        ('critical', 'حرج'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم المعدة")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود المعدة")
    category = models.ForeignKey(EquipmentCategory, on_delete=models.CASCADE, verbose_name="الفئة")
    production_line = models.ForeignKey(ProductionLine, on_delete=models.CASCADE,
                                      related_name='equipment', verbose_name="خط الإنتاج")

    # Technical specifications
    manufacturer = models.CharField(max_length=100, blank=True, verbose_name="الشركة المصنعة")
    model = models.CharField(max_length=100, blank=True, verbose_name="الموديل")
    serial_number = models.CharField(max_length=100, blank=True, verbose_name="الرقم التسلسلي")
    year_manufactured = models.IntegerField(null=True, blank=True, verbose_name="سنة التصنيع")

    # Capacity and performance
    rated_capacity = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                       verbose_name="الطاقة المقدرة")
    capacity_unit = models.CharField(max_length=20, blank=True, verbose_name="وحدة الطاقة")
    power_consumption = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True,
                                          verbose_name="استهلاك الطاقة (كيلو وات)")

    # Status and condition
    status = models.CharField(max_length=20, choices=EQUIPMENT_STATUS_CHOICES,
                            default='operational', verbose_name="الحالة")
    condition = models.CharField(max_length=20, choices=CONDITION_CHOICES,
                               default='good', verbose_name="الحالة الفنية")

    # Dates
    installation_date = models.DateField(null=True, blank=True, verbose_name="تاريخ التركيب")
    last_maintenance_date = models.DateField(null=True, blank=True, verbose_name="تاريخ آخر صيانة")
    next_maintenance_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الصيانة القادمة")

    # Financial
    purchase_cost = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True,
                                      verbose_name="تكلفة الشراء")
    current_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True,
                                      verbose_name="القيمة الحالية")

    # Additional info
    location = models.CharField(max_length=200, blank=True, verbose_name="الموقع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "معدة"
        verbose_name_plural = "المعدات"
        ordering = ['production_line', 'name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def needs_maintenance(self):
        """هل تحتاج صيانة؟"""
        if self.next_maintenance_date:
            from django.utils import timezone
            return timezone.now().date() >= self.next_maintenance_date
        return False

    @property
    def days_until_maintenance(self):
        """عدد الأيام حتى الصيانة القادمة"""
        if self.next_maintenance_date:
            from django.utils import timezone
            delta = self.next_maintenance_date - timezone.now().date()
            return delta.days
        return None

    @property
    def age_in_years(self):
        """عمر المعدة بالسنوات"""
        if self.installation_date:
            from django.utils import timezone
            delta = timezone.now().date() - self.installation_date
            return round(delta.days / 365.25, 1)
        return None


class MaintenanceType(models.Model):
    """أنواع الصيانة"""
    name = models.CharField(max_length=100, verbose_name="اسم نوع الصيانة")
    code = models.CharField(max_length=20, unique=True, verbose_name="الكود")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_preventive = models.BooleanField(default=True, verbose_name="صيانة وقائية")
    estimated_duration_hours = models.DecimalField(max_digits=6, decimal_places=2,
                                                  null=True, blank=True, verbose_name="المدة المقدرة (ساعة)")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "نوع الصيانة"
        verbose_name_plural = "أنواع الصيانة"
        ordering = ['name']

    def __str__(self):
        return self.name


class MaintenanceSchedule(models.Model):
    """جدولة الصيانة"""
    FREQUENCY_CHOICES = [
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('semi_annual', 'نصف سنوي'),
        ('annual', 'سنوي'),
        ('hours_based', 'حسب ساعات التشغيل'),
        ('cycles_based', 'حسب دورات التشغيل'),
    ]

    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE,
                                related_name='maintenance_schedules', verbose_name="المعدة")
    maintenance_type = models.ForeignKey(MaintenanceType, on_delete=models.CASCADE, verbose_name="نوع الصيانة")
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, verbose_name="التكرار")
    frequency_value = models.IntegerField(default=1, verbose_name="قيمة التكرار")

    # For hours/cycles based maintenance
    operating_hours_interval = models.IntegerField(null=True, blank=True,
                                                 verbose_name="فترة ساعات التشغيل")
    cycles_interval = models.IntegerField(null=True, blank=True, verbose_name="فترة الدورات")

    last_maintenance_date = models.DateField(null=True, blank=True, verbose_name="تاريخ آخر صيانة")
    next_maintenance_date = models.DateField(verbose_name="تاريخ الصيانة القادمة")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "جدولة صيانة"
        verbose_name_plural = "جدولة الصيانة"
        ordering = ['next_maintenance_date']

    def __str__(self):
        return f"{self.equipment.name} - {self.maintenance_type.name}"

    @property
    def is_overdue(self):
        """هل متأخرة؟"""
        from django.utils import timezone
        return timezone.now().date() > self.next_maintenance_date

    @property
    def days_until_due(self):
        """عدد الأيام حتى الاستحقاق"""
        from django.utils import timezone
        delta = self.next_maintenance_date - timezone.now().date()
        return delta.days


class MaintenanceRecord(models.Model):
    """سجل الصيانة"""
    STATUS_CHOICES = [
        ('scheduled', 'مجدولة'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغية'),
        ('postponed', 'مؤجلة'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
        ('emergency', 'طوارئ'),
    ]

    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE,
                                related_name='maintenance_records', verbose_name="المعدة")
    maintenance_type = models.ForeignKey(MaintenanceType, on_delete=models.CASCADE, verbose_name="نوع الصيانة")
    schedule = models.ForeignKey(MaintenanceSchedule, on_delete=models.SET_NULL,
                               null=True, blank=True, verbose_name="الجدولة")

    # Basic info
    title = models.CharField(max_length=200, verbose_name="عنوان الصيانة")
    description = models.TextField(verbose_name="وصف الصيانة")
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES,
                              default='normal', verbose_name="الأولوية")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES,
                            default='scheduled', verbose_name="الحالة")

    # Dates and times
    scheduled_date = models.DateTimeField(verbose_name="التاريخ المجدول")
    actual_start_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ البدء الفعلي")
    actual_end_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الانتهاء الفعلي")
    estimated_duration_hours = models.DecimalField(max_digits=6, decimal_places=2,
                                                  verbose_name="المدة المقدرة (ساعة)")
    actual_duration_hours = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True,
                                              verbose_name="المدة الفعلية (ساعة)")

    # Personnel
    assigned_technician = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                          related_name='assigned_maintenances', verbose_name="الفني المكلف")
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='performed_maintenances', verbose_name="نفذ بواسطة")

    # Costs
    labor_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="تكلفة العمالة")
    parts_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="تكلفة القطع")
    external_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="التكلفة الخارجية")

    # Results
    work_performed = models.TextField(blank=True, verbose_name="العمل المنجز")
    parts_replaced = models.TextField(blank=True, verbose_name="القطع المستبدلة")
    findings = models.TextField(blank=True, verbose_name="الملاحظات والنتائج")
    recommendations = models.TextField(blank=True, verbose_name="التوصيات")

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_maintenances')

    class Meta:
        verbose_name = "سجل صيانة"
        verbose_name_plural = "سجلات الصيانة"
        ordering = ['-scheduled_date']

    def __str__(self):
        return f"{self.equipment.name} - {self.title}"

    @property
    def total_cost(self):
        """إجمالي التكلفة"""
        return self.labor_cost + self.parts_cost + self.external_cost

    @property
    def is_overdue(self):
        """هل متأخرة؟"""
        from django.utils import timezone
        return (self.status in ['scheduled', 'in_progress'] and
                timezone.now() > self.scheduled_date)

    @property
    def duration_variance(self):
        """انحراف المدة"""
        if self.actual_duration_hours and self.estimated_duration_hours:
            return self.actual_duration_hours - self.estimated_duration_hours
        return None


class PerformanceMetric(models.Model):
    """مقاييس الأداء"""
    METRIC_TYPE_CHOICES = [
        ('oee', 'الكفاءة الإجمالية للمعدات (OEE)'),
        ('availability', 'معدل التوفر'),
        ('performance', 'معدل الأداء'),
        ('quality', 'معدل الجودة'),
        ('throughput', 'معدل الإنتاجية'),
        ('downtime', 'وقت التوقف'),
        ('efficiency', 'الكفاءة'),
        ('utilization', 'معدل الاستخدام'),
    ]

    production_line = models.ForeignKey(ProductionLine, on_delete=models.CASCADE,
                                      related_name='performance_metrics', verbose_name="خط الإنتاج")
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, null=True, blank=True,
                                related_name='performance_metrics', verbose_name="المعدة")
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPE_CHOICES, verbose_name="نوع المقياس")

    # Time period
    measurement_date = models.DateField(verbose_name="تاريخ القياس")
    shift = models.CharField(max_length=10, choices=ProductionReport.SHIFT,
                           null=True, blank=True, verbose_name="الوردية")

    # Values
    target_value = models.DecimalField(max_digits=10, decimal_places=3, verbose_name="القيمة المستهدفة")
    actual_value = models.DecimalField(max_digits=10, decimal_places=3, verbose_name="القيمة الفعلية")
    unit = models.CharField(max_length=20, verbose_name="الوحدة")

    # Additional data
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "مقياس أداء"
        verbose_name_plural = "مقاييس الأداء"
        ordering = ['-measurement_date', 'production_line']
        unique_together = ['production_line', 'equipment', 'metric_type', 'measurement_date', 'shift']

    def __str__(self):
        equipment_name = f" - {self.equipment.name}" if self.equipment else ""
        return f"{self.production_line.name}{equipment_name} - {self.get_metric_type_display()}"

    @property
    def variance(self):
        """الانحراف عن المستهدف"""
        return self.actual_value - self.target_value

    @property
    def variance_percentage(self):
        """نسبة الانحراف"""
        if self.target_value != 0:
            return (self.variance / self.target_value) * 100
        return 0

    @property
    def achievement_rate(self):
        """معدل الإنجاز"""
        if self.target_value != 0:
            return (self.actual_value / self.target_value) * 100
        return 0


class DowntimeRecord(models.Model):
    """سجل أوقات التوقف"""
    DOWNTIME_TYPE_CHOICES = [
        ('planned', 'توقف مخطط'),
        ('unplanned', 'توقف غير مخطط'),
        ('maintenance', 'صيانة'),
        ('breakdown', 'عطل'),
        ('changeover', 'تغيير المنتج'),
        ('material_shortage', 'نقص المواد'),
        ('quality_issue', 'مشكلة جودة'),
        ('operator_absence', 'غياب المشغل'),
        ('power_outage', 'انقطاع الكهرباء'),
        ('other', 'أخرى'),
    ]

    SEVERITY_CHOICES = [
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('critical', 'حرجة'),
    ]

    production_line = models.ForeignKey(ProductionLine, on_delete=models.CASCADE,
                                      related_name='downtime_records', verbose_name="خط الإنتاج")
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, null=True, blank=True,
                                related_name='downtime_records', verbose_name="المعدة")

    # Basic info
    title = models.CharField(max_length=200, verbose_name="عنوان التوقف")
    downtime_type = models.CharField(max_length=20, choices=DOWNTIME_TYPE_CHOICES, verbose_name="نوع التوقف")
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES, default='medium', verbose_name="الخطورة")

    # Time tracking
    start_time = models.DateTimeField(verbose_name="وقت البدء")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="وقت الانتهاء")
    duration_minutes = models.IntegerField(null=True, blank=True, verbose_name="المدة (دقيقة)")

    # Details
    description = models.TextField(verbose_name="وصف التوقف")
    root_cause = models.TextField(blank=True, verbose_name="السبب الجذري")
    corrective_action = models.TextField(blank=True, verbose_name="الإجراء التصحيحي")
    preventive_action = models.TextField(blank=True, verbose_name="الإجراء الوقائي")

    # Personnel
    reported_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                  related_name='reported_downtimes', verbose_name="أبلغ بواسطة")
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='resolved_downtimes', verbose_name="حل بواسطة")

    # Impact
    production_loss = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True,
                                        verbose_name="خسارة الإنتاج")
    cost_impact = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                    verbose_name="التأثير على التكلفة")

    # Status
    is_resolved = models.BooleanField(default=False, verbose_name="محلول")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "سجل توقف"
        verbose_name_plural = "سجلات التوقف"
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.production_line.name} - {self.title}"

    def save(self, *args, **kwargs):
        # Calculate duration if end_time is set
        if self.end_time and self.start_time:
            delta = self.end_time - self.start_time
            self.duration_minutes = int(delta.total_seconds() / 60)
            self.is_resolved = True
        super().save(*args, **kwargs)

    @property
    def duration_hours(self):
        """المدة بالساعات"""
        if self.duration_minutes:
            return round(self.duration_minutes / 60, 2)
        return None

    @property
    def is_ongoing(self):
        """هل ما زال مستمراً؟"""
        return self.end_time is None
