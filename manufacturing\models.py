from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
from definitions.models import ProductDefinition, WarehouseDefinition


class ProductionLine(models.Model):
    """خط الإنتاج"""
    PRODUCTION_LINE_STATUS = [
        ('active', 'نشط'),
        ('maintenance', 'صيانة'),
        ('inactive', 'غير نشط'),
        ('breakdown', 'عطل'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم خط الإنتاج")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود خط الإنتاج")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    capacity_per_hour = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الطاقة الإنتاجية/ساعة")
    efficiency_rate = models.DecimalField(max_digits=5, decimal_places=2, default=100.00,
                                        validators=[MinValueValidator(0), MaxValueValidator(100)],
                                        verbose_name="معدل الكفاءة %")
    status = models.CharField(max_length=20, choices=PRODUCTION_LINE_STATUS, default='active', verbose_name="الحالة")
    installation_date = models.DateField(verbose_name="تاريخ التركيب")
    last_maintenance = models.DateField(blank=True, null=True, verbose_name="آخر صيانة")
    next_maintenance = models.DateField(blank=True, null=True, verbose_name="الصيانة القادمة")
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                 related_name='supervised_lines', verbose_name="المشرف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_production_lines', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "خط إنتاج"
        verbose_name_plural = "خطوط الإنتاج"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def current_efficiency(self):
        """حساب الكفاءة الحالية"""
        if self.status == 'breakdown':
            return 0
        elif self.status == 'maintenance':
            return 0
        elif self.status == 'inactive':
            return 0
        return self.efficiency_rate

    @property
    def needs_maintenance(self):
        """فحص إذا كان يحتاج صيانة"""
        if self.next_maintenance:
            return timezone.now().date() >= self.next_maintenance
        return False


class BillOfMaterials(models.Model):
    """قائمة المواد (BOM)"""
    BOM_STATUS = [
        ('draft', 'مسودة'),
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('archived', 'مؤرشف'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم قائمة المواد")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود قائمة المواد")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE,
                              related_name='bom_as_product', verbose_name="المنتج النهائي")
    version = models.CharField(max_length=20, default="1.0", verbose_name="الإصدار")
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3, default=1,
                                          validators=[MinValueValidator(0.001)],
                                          verbose_name="الكمية المنتجة")
    production_time_minutes = models.IntegerField(validators=[MinValueValidator(1)],
                                                verbose_name="وقت الإنتاج (دقيقة)")
    labor_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0,
                                   validators=[MinValueValidator(0)], verbose_name="تكلفة العمالة")
    overhead_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0,
                                      validators=[MinValueValidator(0)], verbose_name="التكاليف الإضافية")
    status = models.CharField(max_length=20, choices=BOM_STATUS, default='draft', verbose_name="الحالة")
    effective_date = models.DateField(verbose_name="تاريخ السريان")
    expiry_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الانتهاء")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_boms', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "قائمة مواد"
        verbose_name_plural = "قوائم المواد"
        ordering = ['-created_at']
        unique_together = ['product', 'version']

    def __str__(self):
        return f"{self.name} - {self.product.name} (v{self.version})"

    @property
    def total_material_cost(self):
        """إجمالي تكلفة المواد"""
        return sum(item.total_cost for item in self.bom_items.all())

    @property
    def total_cost(self):
        """إجمالي التكلفة"""
        return self.total_material_cost + self.labor_cost + self.overhead_cost

    @property
    def cost_per_unit(self):
        """تكلفة الوحدة"""
        if self.quantity_produced > 0:
            return self.total_cost / self.quantity_produced
        return 0


class BOMItem(models.Model):
    """عنصر في قائمة المواد"""
    ITEM_TYPE = [
        ('raw_material', 'مادة خام'),
        ('component', 'مكون'),
        ('sub_assembly', 'تجميع فرعي'),
    ]

    bom = models.ForeignKey(BillOfMaterials, on_delete=models.CASCADE,
                          related_name='bom_items', verbose_name="قائمة المواد")
    material = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE,
                               related_name='bom_as_material', verbose_name="المادة")
    item_type = models.CharField(max_length=20, choices=ITEM_TYPE, default='raw_material',
                               verbose_name="نوع العنصر")
    quantity_required = models.DecimalField(max_digits=10, decimal_places=3,
                                          validators=[MinValueValidator(0.001)],
                                          verbose_name="الكمية المطلوبة")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2,
                                  validators=[MinValueValidator(0)], verbose_name="تكلفة الوحدة")
    waste_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0,
                                         validators=[MinValueValidator(0), MaxValueValidator(100)],
                                         verbose_name="نسبة الفاقد %")
    is_critical = models.BooleanField(default=False, verbose_name="مادة حرجة")
    supplier_lead_time = models.IntegerField(default=0, verbose_name="مدة التوريد (أيام)")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    sequence = models.IntegerField(default=1, verbose_name="التسلسل")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "عنصر قائمة مواد"
        verbose_name_plural = "عناصر قوائم المواد"
        ordering = ['sequence', 'material__name']
        unique_together = ['bom', 'material']

    def __str__(self):
        return f"{self.material.name} - {self.quantity_required}"

    @property
    def total_cost(self):
        """إجمالي التكلفة مع الفاقد"""
        base_cost = self.quantity_required * self.unit_cost
        waste_cost = base_cost * (self.waste_percentage / 100)
        return base_cost + waste_cost

    @property
    def quantity_with_waste(self):
        """الكمية مع الفاقد"""
        return self.quantity_required * (1 + self.waste_percentage / 100)


class ProductionOrder(models.Model):
    """أمر إنتاج"""
    ORDER_STATUS = [
        ('draft', 'مسودة'),
        ('planned', 'مخطط'),
        ('released', 'محرر'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
        ('on_hold', 'معلق'),
    ]

    PRIORITY = [
        ('low', 'منخفض'),
        ('normal', 'عادي'),
        ('high', 'عالي'),
        ('urgent', 'عاجل'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الأمر")
    bom = models.ForeignKey(BillOfMaterials, on_delete=models.CASCADE, verbose_name="قائمة المواد")
    production_line = models.ForeignKey(ProductionLine, on_delete=models.CASCADE, verbose_name="خط الإنتاج")
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=3,
                                         validators=[MinValueValidator(0.001)],
                                         verbose_name="الكمية المطلوبة")
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المنتجة")
    quantity_scrapped = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية التالفة")
    status = models.CharField(max_length=20, choices=ORDER_STATUS, default='draft', verbose_name="الحالة")
    priority = models.CharField(max_length=10, choices=PRIORITY, default='normal', verbose_name="الأولوية")
    planned_start_date = models.DateTimeField(verbose_name="تاريخ البدء المخطط")
    planned_end_date = models.DateTimeField(verbose_name="تاريخ الانتهاء المخطط")
    actual_start_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ البدء الفعلي")
    actual_end_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ الانتهاء الفعلي")
    customer_order_ref = models.CharField(max_length=100, blank=True, null=True,
                                        verbose_name="مرجع طلب العميل")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_production_orders', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "أمر إنتاج"
        verbose_name_plural = "أوامر الإنتاج"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.bom.product.name}"

    @property
    def completion_percentage(self):
        """نسبة الإنجاز"""
        if self.quantity_ordered > 0:
            return min((self.quantity_produced / self.quantity_ordered) * 100, 100)
        return 0

    @property
    def is_overdue(self):
        """فحص إذا كان متأخر"""
        if self.planned_end_date and self.status not in ['completed', 'cancelled']:
            return timezone.now() > self.planned_end_date
        return False

    @property
    def estimated_cost(self):
        """التكلفة المقدرة"""
        return self.bom.cost_per_unit * self.quantity_ordered


class ProductionOrderItem(models.Model):
    """عنصر أمر الإنتاج"""
    CONSUMPTION_STATUS = [
        ('planned', 'مخطط'),
        ('reserved', 'محجوز'),
        ('consumed', 'مستهلك'),
        ('returned', 'مرتجع'),
    ]

    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE,
                                       related_name='order_items', verbose_name="أمر الإنتاج")
    bom_item = models.ForeignKey(BOMItem, on_delete=models.CASCADE, verbose_name="عنصر قائمة المواد")
    quantity_required = models.DecimalField(max_digits=10, decimal_places=3,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المطلوبة")
    quantity_consumed = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المستهلكة")
    quantity_returned = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المرتجعة")
    status = models.CharField(max_length=20, choices=CONSUMPTION_STATUS, default='planned',
                            verbose_name="حالة الاستهلاك")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE,
                                verbose_name="المخزن")
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="رقم الدفعة")
    expiry_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الانتهاء")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "عنصر أمر إنتاج"
        verbose_name_plural = "عناصر أوامر الإنتاج"
        ordering = ['bom_item__sequence']

    def __str__(self):
        return f"{self.production_order.order_number} - {self.bom_item.material.name}"

    @property
    def consumption_percentage(self):
        """نسبة الاستهلاك"""
        if self.quantity_required > 0:
            return (self.quantity_consumed / self.quantity_required) * 100
        return 0


class QualityCheck(models.Model):
    """فحص الجودة"""
    CHECK_STATUS = [
        ('pending', 'معلق'),
        ('in_progress', 'قيد الفحص'),
        ('passed', 'نجح'),
        ('failed', 'فشل'),
        ('conditional', 'مشروط'),
    ]

    CHECK_TYPE = [
        ('incoming', 'فحص المواد الواردة'),
        ('in_process', 'فحص أثناء الإنتاج'),
        ('final', 'فحص نهائي'),
        ('random', 'فحص عشوائي'),
    ]

    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE,
                                       related_name='quality_checks', verbose_name="أمر الإنتاج")
    check_type = models.CharField(max_length=20, choices=CHECK_TYPE, verbose_name="نوع الفحص")
    check_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الفحص")
    inspector = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                related_name='quality_inspections', verbose_name="المفتش")
    quantity_checked = models.DecimalField(max_digits=10, decimal_places=3,
                                         validators=[MinValueValidator(0.001)],
                                         verbose_name="الكمية المفحوصة")
    quantity_passed = models.DecimalField(max_digits=10, decimal_places=3,
                                        validators=[MinValueValidator(0)],
                                        verbose_name="الكمية الناجحة")
    quantity_failed = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                        validators=[MinValueValidator(0)],
                                        verbose_name="الكمية الفاشلة")
    status = models.CharField(max_length=20, choices=CHECK_STATUS, default='pending',
                            verbose_name="حالة الفحص")
    defect_description = models.TextField(blank=True, null=True, verbose_name="وصف العيوب")
    corrective_action = models.TextField(blank=True, null=True, verbose_name="الإجراء التصحيحي")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "فحص جودة"
        verbose_name_plural = "فحوصات الجودة"
        ordering = ['-check_date']

    def __str__(self):
        return f"{self.production_order.order_number} - {self.get_check_type_display()}"

    @property
    def pass_rate(self):
        """معدل النجاح"""
        if self.quantity_checked > 0:
            return (self.quantity_passed / self.quantity_checked) * 100
        return 0


class ProductionReport(models.Model):
    """تقرير الإنتاج"""
    SHIFT = [
        ('morning', 'الصباحية'),
        ('evening', 'المسائية'),
        ('night', 'الليلية'),
    ]

    production_order = models.ForeignKey(ProductionOrder, on_delete=models.CASCADE,
                                       related_name='production_reports', verbose_name="أمر الإنتاج")
    report_date = models.DateField(verbose_name="تاريخ التقرير")
    shift = models.CharField(max_length=10, choices=SHIFT, verbose_name="الوردية")
    operator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                               related_name='production_reports', verbose_name="المشغل")
    quantity_produced = models.DecimalField(max_digits=10, decimal_places=3,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية المنتجة")
    quantity_scrapped = models.DecimalField(max_digits=10, decimal_places=3, default=0,
                                          validators=[MinValueValidator(0)],
                                          verbose_name="الكمية التالفة")
    downtime_minutes = models.IntegerField(default=0, validators=[MinValueValidator(0)],
                                         verbose_name="وقت التوقف (دقيقة)")
    downtime_reason = models.TextField(blank=True, null=True, verbose_name="سبب التوقف")
    efficiency_rate = models.DecimalField(max_digits=5, decimal_places=2,
                                        validators=[MinValueValidator(0), MaxValueValidator(100)],
                                        verbose_name="معدل الكفاءة %")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True,
                                 related_name='created_production_reports', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تقرير إنتاج"
        verbose_name_plural = "تقارير الإنتاج"
        ordering = ['-report_date', '-shift']
        unique_together = ['production_order', 'report_date', 'shift']

    def __str__(self):
        return f"{self.production_order.order_number} - {self.report_date} ({self.get_shift_display()})"

    @property
    def scrap_rate(self):
        """معدل التلف"""
        total_produced = self.quantity_produced + self.quantity_scrapped
        if total_produced > 0:
            return (self.quantity_scrapped / total_produced) * 100
        return 0
