{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .lines-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .line-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .line-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .line-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    }

    .line-name {
        font-size: 1.3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        margin: 2rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-diagram-3 me-3"></i>
                    خطوط الإنتاج
                </h1>
                <p class="text-white-50 mb-0 mt-2">إدارة خطوط الإنتاج والمعدات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:production_line_create' %}" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>خط إنتاج جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- محتوى مؤقت -->
    <div class="empty-state">
        <i class="bi bi-diagram-3" style="font-size: 4rem; color: #667eea; margin-bottom: 1rem;"></i>
        <h3 style="color: #2c3e50;">خطوط الإنتاج</h3>
        <p style="color: #6c757d;">هذه الصفحة قيد التطوير وستكون جاهزة قريباً</p>
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-primary">
                <i class="bi bi-house me-2"></i>العودة للوحة التحكم
            </a>
            <a href="{% url 'manufacturing:production_line_create' %}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-2"></i>إنشاء خط إنتاج
            </a>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="text-center mt-4" style="background: rgba(255, 255, 255, 0.9); padding: 2rem; border-radius: 20px;">
        <h5 style="color: #2c3e50;">قريباً: ميزات خطوط الإنتاج</h5>
        <p style="color: #6c757d;">
            • إدارة المعدات والآلات<br>
            • تتبع الطاقة الإنتاجية<br>
            • جدولة الصيانة<br>
            • مراقبة الكفاءة
        </p>
    </div>
</div>
{% endblock %}
