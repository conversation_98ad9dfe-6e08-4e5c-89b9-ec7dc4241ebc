from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import (
    ProductionLine, WorkCenter, BillOfMaterials, BOMItem, ProductionOrder
)
from definitions.models import WarehouseDefinition, ProductDefinition, PersonDefinition


class ProductionLineForm(forms.ModelForm):
    """نموذج خط الإنتاج"""
    
    class Meta:
        model = ProductionLine
        fields = [
            'name', 'code', 'description', 'warehouse', 'supervisor',
            'capacity_per_hour', 'efficiency_rate', 'installation_date',
            'status', 'setup_cost', 'operating_cost_per_hour', 'notes', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم خط الإنتاج'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'كود خط الإنتاج'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف خط الإنتاج'
            }),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'supervisor': forms.Select(attrs={'class': 'form-select'}),
            'capacity_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'efficiency_rate': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'installation_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'setup_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'operating_cost_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['warehouse'].queryset = WarehouseDefinition.objects.filter(is_active=True)


class WorkCenterForm(forms.ModelForm):
    """نموذج مركز العمل"""
    
    class Meta:
        model = WorkCenter
        fields = [
            'name', 'code', 'production_line', 'description',
            'capacity_per_hour', 'setup_time_minutes',
            'labor_cost_per_hour', 'overhead_cost_per_hour', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم مركز العمل'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'كود مركز العمل'
            }),
            'production_line': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف مركز العمل'
            }),
            'capacity_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'setup_time_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'labor_cost_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'overhead_cost_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['production_line'].queryset = ProductionLine.objects.filter(is_active=True)


class BillOfMaterialsForm(forms.ModelForm):
    """نموذج قائمة المواد"""
    
    class Meta:
        model = BillOfMaterials
        fields = [
            'name', 'code', 'product', 'version', 'quantity_produced',
            'production_time_minutes', 'labor_cost', 'overhead_cost',
            'effective_date', 'expiry_date', 'status', 'notes', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم قائمة المواد'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'كود قائمة المواد'
            }),
            'product': forms.Select(attrs={'class': 'form-select'}),
            'version': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '1.0'
            }),
            'quantity_produced': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'production_time_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'labor_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'overhead_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'effective_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'expiry_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = ProductDefinition.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        effective_date = cleaned_data.get('effective_date')
        expiry_date = cleaned_data.get('expiry_date')

        if effective_date and expiry_date:
            if expiry_date <= effective_date:
                raise ValidationError('تاريخ الانتهاء يجب أن يكون بعد تاريخ السريان')

        return cleaned_data


class BOMItemForm(forms.ModelForm):
    """نموذج عنصر قائمة المواد"""
    
    class Meta:
        model = BOMItem
        fields = [
            'bom', 'material', 'sequence', 'item_type', 'quantity_required',
            'unit_cost', 'waste_percentage', 'is_critical', 'lead_time_days',
            'supplier', 'notes', 'is_active'
        ]
        widgets = {
            'bom': forms.Select(attrs={'class': 'form-select'}),
            'material': forms.Select(attrs={'class': 'form-select'}),
            'sequence': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'item_type': forms.Select(attrs={'class': 'form-select'}),
            'quantity_required': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'waste_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'is_critical': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'lead_time_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'supplier': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات إضافية'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['bom'].queryset = BillOfMaterials.objects.filter(is_active=True)
        self.fields['material'].queryset = ProductDefinition.objects.filter(is_active=True)
        self.fields['supplier'].queryset = PersonDefinition.objects.filter(is_active=True, person_type='supplier')


class ProductionOrderForm(forms.ModelForm):
    """نموذج أمر الإنتاج"""
    
    class Meta:
        model = ProductionOrder
        fields = [
            'order_number', 'bom', 'production_line', 'quantity_ordered',
            'planned_start_date', 'planned_end_date', 'status', 'priority',
            'planner', 'supervisor', 'notes', 'is_active'
        ]
        widgets = {
            'order_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الأمر (اتركه فارغاً للإنشاء التلقائي)'
            }),
            'bom': forms.Select(attrs={'class': 'form-select'}),
            'production_line': forms.Select(attrs={'class': 'form-select'}),
            'quantity_ordered': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'planned_start_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'planned_end_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'priority': forms.Select(attrs={'class': 'form-select'}),
            'planner': forms.Select(attrs={'class': 'form-select'}),
            'supervisor': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['bom'].queryset = BillOfMaterials.objects.filter(is_active=True, status='active')
        self.fields['production_line'].queryset = ProductionLine.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        planned_start_date = cleaned_data.get('planned_start_date')
        planned_end_date = cleaned_data.get('planned_end_date')

        if planned_start_date and planned_end_date:
            if planned_end_date <= planned_start_date:
                raise ValidationError('تاريخ الانتهاء المخطط يجب أن يكون بعد تاريخ البدء المخطط')

        return cleaned_data
