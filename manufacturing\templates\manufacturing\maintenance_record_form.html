{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin: 2rem 0;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: rgba(52, 152, 219, 0.05);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(52, 152, 219, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #3498db;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid rgba(52, 152, 219, 0.2);
        border-radius: 12px;
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        background: white;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .alert-custom {
        border: none;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        background: rgba(52, 152, 219, 0.1);
        color: #3498db;
        border-left: 4px solid #3498db;
    }

    .form-text-custom {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-tools me-3"></i>
                    {% if maintenance_record %}تحديث سجل الصيانة{% else %}إنشاء سجل صيانة جديد{% endif %}
                </h1>
                <p class="text-white-50 mb-0 mt-2">
                    {% if maintenance_record %}تحديث بيانات سجل الصيانة{% else %}إنشاء سجل صيانة جديد للمعدات{% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:maintenance_record_list' %}" class="btn btn-light">
                        <i class="bi bi-tools me-2"></i>سجلات الصيانة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- النموذج -->
    <div class="form-container">
        <div class="alert-custom">
            <i class="bi bi-info-circle me-2"></i>
            <strong>ملاحظة:</strong> تأكد من إدخال جميع البيانات المطلوبة بدقة لضمان تتبع فعال لأعمال الصيانة.
        </div>

        <form method="post" id="maintenanceForm">
            {% csrf_token %}
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    معلومات الصيانة الأساسية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">المعدة</label>
                            {{ form.equipment }}
                            <div class="form-text-custom">اختر المعدة التي تحتاج صيانة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">نوع الصيانة</label>
                            {{ form.maintenance_type }}
                            <div class="form-text-custom">نوع الصيانة المطلوبة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label required-field">عنوان الصيانة</label>
                            {{ form.title }}
                            <div class="form-text-custom">عنوان وصفي لعملية الصيانة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">وصف الصيانة</label>
                            {{ form.description }}
                            <div class="form-text-custom">وصف تفصيلي للأعمال المطلوبة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">الأولوية</label>
                            {{ form.priority }}
                            <div class="form-text-custom">مستوى أولوية الصيانة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">الحالة</label>
                            {{ form.status }}
                            <div class="form-text-custom">حالة سجل الصيانة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التوقيت والجدولة -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-clock"></i>
                    التوقيت والجدولة
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">التاريخ المجدول</label>
                            {{ form.scheduled_date }}
                            <div class="form-text-custom">التاريخ والوقت المجدول للصيانة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المدة المقدرة (ساعات)</label>
                            {{ form.estimated_duration_hours }}
                            <div class="form-text-custom">المدة المتوقعة لإنجاز الصيانة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تاريخ البدء الفعلي</label>
                            {{ form.actual_start_date }}
                            <div class="form-text-custom">التاريخ الفعلي لبدء الصيانة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تاريخ الانتهاء الفعلي</label>
                            {{ form.actual_end_date }}
                            <div class="form-text-custom">التاريخ الفعلي لانتهاء الصيانة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الموظفون والتكاليف -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-people"></i>
                    الموظفون والتكاليف
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الفني المكلف</label>
                            {{ form.assigned_technician }}
                            <div class="form-text-custom">الفني المسؤول عن الصيانة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نفذ بواسطة</label>
                            {{ form.performed_by }}
                            <div class="form-text-custom">الفني الذي نفذ الصيانة فعلياً</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تكلفة العمالة</label>
                            {{ form.labor_cost }}
                            <div class="form-text-custom">تكلفة العمالة والأجور</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">تكلفة قطع الغيار</label>
                            {{ form.parts_cost }}
                            <div class="form-text-custom">تكلفة قطع الغيار المستخدمة</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">التكلفة الخارجية</label>
                            {{ form.external_cost }}
                            <div class="form-text-custom">تكلفة الخدمات الخارجية</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- النتائج والتوصيات -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-clipboard-check"></i>
                    النتائج والتوصيات
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الأعمال المنجزة</label>
                            {{ form.work_performed }}
                            <div class="form-text-custom">وصف الأعمال التي تم إنجازها</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">قطع الغيار المستبدلة</label>
                            {{ form.parts_replaced }}
                            <div class="form-text-custom">قائمة بقطع الغيار المستبدلة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">النتائج والملاحظات</label>
                            {{ form.findings }}
                            <div class="form-text-custom">النتائج والملاحظات المهمة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">التوصيات</label>
                            {{ form.recommendations }}
                            <div class="form-text-custom">التوصيات للصيانة المستقبلية</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-3 justify-content-between align-items-center">
                <!-- أزرار العودة -->
                <div class="d-flex gap-2">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-2"></i>لوحة التحكم
                    </a>
                    <a href="{% url 'manufacturing:maintenance_record_list' %}" class="btn btn-outline-primary">
                        <i class="bi bi-tools me-2"></i>سجلات الصيانة
                    </a>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="d-flex gap-3">
                    <a href="{% url 'manufacturing:maintenance_record_list' %}" class="btn-secondary-custom">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                    <button type="submit" class="btn-primary-custom">
                        <i class="bi bi-check-circle"></i>
                        {% if maintenance_record %}تحديث السجل{% else %}إنشاء السجل{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('maintenanceForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    // تعيين التاريخ الحالي كتاريخ مجدول افتراضي
    const scheduledDateInput = form.querySelector('input[name="scheduled_date"]');
    if (scheduledDateInput && !scheduledDateInput.value) {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000);
        scheduledDateInput.value = localDateTime.toISOString().slice(0, 16);
    }
});
</script>
{% endblock %}
