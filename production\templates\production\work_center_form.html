{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 3rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <h1 class="page-title">
            <i class="bi bi-plus-circle me-3"></i>
            {{ page_title }}
        </h1>
    </div>

    <div class="form-card">
        <form method="post">
            {% csrf_token %}
            <div class="row g-3">
                {% for field in form %}
                <div class="col-md-6">
                    <label class="form-label">{{ field.label }}</label>
                    {{ field }}
                </div>
                {% endfor %}
            </div>
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="bi bi-check-circle me-2"></i>حفظ
                </button>
                <a href="{% url 'production:work_center_list' %}" class="btn btn-secondary btn-lg">
                    <i class="bi bi-x-circle me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
