{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin: 2rem 0;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: rgba(52, 152, 219, 0.05);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(52, 152, 219, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #3498db;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid rgba(52, 152, 219, 0.2);
        border-radius: 12px;
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        background: white;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .alert-custom {
        border: none;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        background: rgba(52, 152, 219, 0.1);
        color: #3498db;
        border-left: 4px solid #3498db;
    }

    .form-text-custom {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .metric-type-info {
        background: rgba(52, 152, 219, 0.1);
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .metric-type-info h6 {
        color: #3498db;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .metric-type-info ul {
        margin: 0;
        padding-left: 1.5rem;
    }

    .metric-type-info li {
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-graph-up me-3"></i>
                    إضافة مقياس أداء جديد
                </h1>
                <p class="text-white-50 mb-0 mt-2">إضافة مقياس أداء جديد لتتبع كفاءة خطوط الإنتاج</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:performance_dashboard' %}" class="btn btn-light">
                        <i class="bi bi-speedometer2 me-2"></i>مراقبة الأداء
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- النموذج -->
    <div class="form-container">
        <div class="alert-custom">
            <i class="bi bi-info-circle me-2"></i>
            <strong>ملاحظة:</strong> تأكد من إدخال جميع البيانات بدقة لضمان تتبع صحيح لأداء خطوط الإنتاج.
        </div>

        <form method="post" id="metricForm">
            {% csrf_token %}
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    معلومات المقياس الأساسية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">خط الإنتاج</label>
                            {{ form.production_line }}
                            <div class="form-text-custom">اختر خط الإنتاج المراد قياس أدائه</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المعدة (اختياري)</label>
                            {{ form.equipment }}
                            <div class="form-text-custom">اختر معدة محددة أو اتركها فارغة للخط بالكامل</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">نوع المقياس</label>
                            {{ form.metric_type }}
                            <div class="form-text-custom">نوع مقياس الأداء المراد تسجيله</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الوردية</label>
                            {{ form.shift }}
                            <div class="form-text-custom">الوردية التي تم قياس الأداء فيها</div>
                        </div>
                    </div>
                </div>

                <div class="metric-type-info">
                    <h6>أنواع مقاييس الأداء:</h6>
                    <ul>
                        <li><strong>OEE (الكفاءة الإجمالية):</strong> مقياس شامل للأداء العام</li>
                        <li><strong>التوفر:</strong> نسبة الوقت المتاح للإنتاج</li>
                        <li><strong>الأداء:</strong> سرعة الإنتاج مقارنة بالمعدل المثالي</li>
                        <li><strong>الجودة:</strong> نسبة المنتجات الجيدة من إجمالي الإنتاج</li>
                    </ul>
                </div>
            </div>

            <!-- القيم والقياسات -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-calculator"></i>
                    القيم والقياسات
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">تاريخ القياس</label>
                            {{ form.measurement_date }}
                            <div class="form-text-custom">التاريخ الذي تم فيه أخذ القياس</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">وحدة القياس</label>
                            {{ form.unit }}
                            <div class="form-text-custom">وحدة قياس المقياس (مثل: %, قطعة/ساعة)</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">القيمة المستهدفة</label>
                            {{ form.target_value }}
                            <div class="form-text-custom">القيمة المستهدفة أو المخططة</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">القيمة الفعلية</label>
                            {{ form.actual_value }}
                            <div class="form-text-custom">القيمة الفعلية المحققة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-file-text"></i>
                    ملاحظات إضافية
                </h3>
                
                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    {{ form.notes }}
                    <div class="form-text-custom">أي ملاحظات أو تفاصيل إضافية حول القياس</div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-3 justify-content-between align-items-center">
                <!-- أزرار العودة -->
                <div class="d-flex gap-2">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-2"></i>لوحة التحكم
                    </a>
                    <a href="{% url 'manufacturing:performance_dashboard' %}" class="btn btn-outline-primary">
                        <i class="bi bi-speedometer2 me-2"></i>مراقبة الأداء
                    </a>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="d-flex gap-3">
                    <a href="{% url 'manufacturing:performance_dashboard' %}" class="btn-secondary-custom">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                    <button type="submit" class="btn-primary-custom">
                        <i class="bi bi-check-circle"></i>
                        إضافة المقياس
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('metricForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    // تعيين التاريخ الحالي كتاريخ قياس افتراضي
    const measurementDateInput = form.querySelector('input[name="measurement_date"]');
    if (measurementDateInput && !measurementDateInput.value) {
        const today = new Date();
        measurementDateInput.value = today.toISOString().split('T')[0];
    }

    // تحديث المعدات بناءً على خط الإنتاج المختار
    const productionLineSelect = form.querySelector('select[name="production_line"]');
    const equipmentSelect = form.querySelector('select[name="equipment"]');
    
    if (productionLineSelect && equipmentSelect) {
        productionLineSelect.addEventListener('change', function() {
            const lineId = this.value;
            if (lineId) {
                // يمكن إضافة AJAX لتحديث قائمة المعدات
                // بناءً على خط الإنتاج المختار
            }
        });
    }

    // تحديث وحدة القياس بناءً على نوع المقياس
    const metricTypeSelect = form.querySelector('select[name="metric_type"]');
    const unitInput = form.querySelector('input[name="unit"]');
    
    if (metricTypeSelect && unitInput) {
        metricTypeSelect.addEventListener('change', function() {
            const metricType = this.value;
            if (metricType === 'oee' || metricType === 'availability' || 
                metricType === 'performance' || metricType === 'quality') {
                unitInput.value = '%';
            } else {
                unitInput.value = '';
            }
        });
    }
});
</script>
{% endblock %}
