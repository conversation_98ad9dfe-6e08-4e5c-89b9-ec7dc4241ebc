{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <h1 class="page-title">
            <i class="bi bi-tools me-3"></i>
            {{ center.name }}
        </h1>
    </div>

    <div class="content-card">
        <h3>معلومات مركز العمل</h3>
        <div class="row g-3">
            <div class="col-md-6">
                <strong>الاسم:</strong> {{ center.name }}
            </div>
            <div class="col-md-6">
                <strong>الكود:</strong> {{ center.code }}
            </div>
            <div class="col-md-6">
                <strong>خط الإنتاج:</strong> {{ center.production_line.name }}
            </div>
            <div class="col-md-6">
                <strong>الطاقة الإنتاجية:</strong> {{ center.capacity_per_hour }} وحدة/ساعة
            </div>
            <div class="col-md-6">
                <strong>وقت الإعداد:</strong> {{ center.setup_time_minutes }} دقيقة
            </div>
            <div class="col-md-6">
                <strong>تكلفة العمالة/ساعة:</strong> {{ center.labor_cost_per_hour }} جنيه
            </div>
            <div class="col-md-6">
                <strong>التكاليف الإضافية/ساعة:</strong> {{ center.overhead_cost_per_hour }} جنيه
            </div>
            <div class="col-md-6">
                <strong>الحالة:</strong> 
                {% if center.is_active %}
                    <span class="badge bg-success">نشط</span>
                {% else %}
                    <span class="badge bg-secondary">غير نشط</span>
                {% endif %}
            </div>
        </div>
        
        {% if center.description %}
        <div class="mt-3">
            <strong>الوصف:</strong>
            <p class="mt-2">{{ center.description }}</p>
        </div>
        {% endif %}
    </div>

    <div class="text-center">
        <a href="{% url 'production:work_center_update' center.pk %}" class="btn btn-primary me-3">
            <i class="bi bi-pencil me-2"></i>تعديل
        </a>
        <a href="{% url 'production:work_center_list' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}
