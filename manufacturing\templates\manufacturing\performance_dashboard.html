{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .kpi-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .kpi-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .kpi-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .kpi-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    .kpi-number {
        font-size: 2.5rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .kpi-label {
        color: #6c757d;
        font-weight: 600;
        font-size: 1rem;
    }

    .kpi-trend {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .trend-up {
        background: #27ae60;
        color: white;
    }

    .trend-down {
        background: #e74c3c;
        color: white;
    }

    .trend-stable {
        background: #f39c12;
        color: white;
    }

    .data-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #667eea;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .metric-card {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        padding: 1.5rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .metric-name {
        font-weight: 700;
        color: #2c3e50;
    }

    .metric-type {
        background: #667eea;
        color: white;
        padding: 0.2rem 0.6rem;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .metric-values {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .metric-value {
        text-align: center;
    }

    .metric-value-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
    }

    .metric-value-label {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 600;
    }

    .downtime-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .downtime-card {
        background: rgba(231, 76, 60, 0.05);
        border-radius: 15px;
        padding: 1.5rem;
        border: 1px solid rgba(231, 76, 60, 0.1);
        transition: all 0.3s ease;
    }

    .downtime-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .downtime-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .downtime-title {
        font-weight: 700;
        color: #2c3e50;
    }

    .downtime-type {
        background: #e74c3c;
        color: white;
        padding: 0.2rem 0.6rem;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .downtime-info {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .downtime-duration {
        color: #e74c3c;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .chart-container {
        height: 300px;
        background: white;
        border-radius: 15px;
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-style: italic;
    }

    .btn-action {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 0.8rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-speedometer2 me-3"></i>
                    مراقبة الأداء
                </h1>
                <p class="text-white-50 mb-0 mt-2">تتبع ومراقبة مؤشرات الأداء الرئيسية والكفاءة</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="#" class="btn btn-light btn-lg">
                        <i class="bi bi-graph-up me-2"></i>تقرير مفصل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشرات الأداء الرئيسية -->
    <div class="kpi-grid">
        <div class="kpi-card">
            <div class="kpi-trend trend-up">
                <i class="bi bi-arrow-up"></i> +5%
            </div>
            <div class="kpi-icon" style="background: var(--success-gradient);">
                <i class="bi bi-speedometer2"></i>
            </div>
            <div class="kpi-number">{{ avg_oee }}%</div>
            <div class="kpi-label">الكفاءة الإجمالية (OEE)</div>
        </div>

        <div class="kpi-card">
            <div class="kpi-trend trend-stable">
                <i class="bi bi-dash"></i> 0%
            </div>
            <div class="kpi-icon" style="background: var(--info-gradient);">
                <i class="bi bi-clock"></i>
            </div>
            <div class="kpi-number">{{ total_downtime_hours }}</div>
            <div class="kpi-label">ساعات التوقف (أسبوع)</div>
        </div>

        <div class="kpi-card">
            <div class="kpi-trend trend-up">
                <i class="bi bi-arrow-up"></i> +12%
            </div>
            <div class="kpi-icon" style="background: var(--primary-gradient);">
                <i class="bi bi-graph-up"></i>
            </div>
            <div class="kpi-number">87.5%</div>
            <div class="kpi-label">معدل التوفر</div>
        </div>

        <div class="kpi-card">
            <div class="kpi-trend trend-up">
                <i class="bi bi-arrow-up"></i> +8%
            </div>
            <div class="kpi-icon" style="background: var(--warning-gradient);">
                <i class="bi bi-shield-check"></i>
            </div>
            <div class="kpi-number">94.2%</div>
            <div class="kpi-label">معدل الجودة</div>
        </div>
    </div>

    <!-- مقاييس الأداء الحديثة -->
    {% if recent_metrics %}
    <div class="data-section">
        <h3 class="section-title">
            <i class="bi bi-graph-up"></i>
            مقاييس الأداء الحديثة
        </h3>
        
        <div class="metrics-grid">
            {% for metric in recent_metrics %}
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-name">
                        {{ metric.production_line.name }}
                        {% if metric.equipment %} - {{ metric.equipment.name }}{% endif %}
                    </div>
                    <div class="metric-type">{{ metric.get_metric_type_display }}</div>
                </div>
                
                <div class="metric-values">
                    <div class="metric-value">
                        <div class="metric-value-number">{{ metric.target_value|floatformat:1 }}</div>
                        <div class="metric-value-label">المستهدف</div>
                    </div>
                    <div class="metric-value">
                        <div class="metric-value-number">{{ metric.actual_value|floatformat:1 }}</div>
                        <div class="metric-value-label">الفعلي</div>
                    </div>
                    <div class="metric-value">
                        <div class="metric-value-number 
                            {% if metric.achievement_rate >= 100 %}text-success
                            {% elif metric.achievement_rate >= 80 %}text-warning
                            {% else %}text-danger{% endif %}">
                            {{ metric.achievement_rate|floatformat:1 }}%
                        </div>
                        <div class="metric-value-label">الإنجاز</div>
                    </div>
                </div>
                
                {% progress_bar metric.actual_value metric.target_value "" %}
                
                <div class="text-muted small">
                    {{ metric.measurement_date|date:"Y/m/d" }}
                    {% if metric.shift %} - {{ metric.get_shift_display }}{% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- أحدث أوقات التوقف -->
    {% if recent_downtime %}
    <div class="data-section">
        <h3 class="section-title">
            <i class="bi bi-exclamation-triangle"></i>
            أحدث أوقات التوقف
        </h3>
        
        <div class="downtime-grid">
            {% for downtime in recent_downtime %}
            <div class="downtime-card">
                <div class="downtime-header">
                    <div class="downtime-title">{{ downtime.title }}</div>
                    <div class="downtime-type">{{ downtime.get_downtime_type_display }}</div>
                </div>
                
                <div class="downtime-info">
                    <strong>خط الإنتاج:</strong> {{ downtime.production_line.name }}
                </div>
                {% if downtime.equipment %}
                <div class="downtime-info">
                    <strong>المعدة:</strong> {{ downtime.equipment.name }}
                </div>
                {% endif %}
                <div class="downtime-info">
                    <strong>بدء التوقف:</strong> {{ downtime.start_time|date:"Y/m/d H:i" }}
                </div>
                {% if downtime.end_time %}
                <div class="downtime-info">
                    <strong>انتهاء التوقف:</strong> {{ downtime.end_time|date:"Y/m/d H:i" }}
                </div>
                <div class="downtime-duration">
                    المدة: {{ downtime.duration_hours|floatformat:1 }} ساعة
                </div>
                {% else %}
                <div class="downtime-duration text-danger">
                    <i class="bi bi-clock"></i> مستمر
                </div>
                {% endif %}
                
                {% if downtime.severity %}
                <div class="mt-2">
                    <span class="badge bg-{{ downtime.severity|status_color }}">
                        {{ downtime.get_severity_display }}
                    </span>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-3">
            <a href="{% url 'manufacturing:downtime_record_list' %}" class="btn btn-primary btn-lg">
                <i class="bi bi-list-ul me-2"></i>عرض جميع سجلات التوقف
            </a>
        </div>
    </div>
    {% endif %}

    <!-- الرسوم البيانية -->
    <div class="data-section">
        <h3 class="section-title">
            <i class="bi bi-bar-chart"></i>
            الرسوم البيانية
        </h3>
        
        <div class="row">
            <div class="col-md-6">
                <h5>اتجاه الكفاءة الإجمالية (OEE)</h5>
                <div class="chart-container">
                    الرسم البياني قيد التطوير
                </div>
            </div>
            <div class="col-md-6">
                <h5>توزيع أوقات التوقف</h5>
                <div class="chart-container">
                    الرسم البياني قيد التطوير
                </div>
            </div>
        </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="data-section">
        <h3 class="section-title">
            <i class="bi bi-lightning-charge"></i>
            إجراءات سريعة
        </h3>
        
        <div class="row">
            <div class="col-md-3">
                <a href="{% url 'manufacturing:downtime_record_create' %}" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-plus-circle"></i>تسجيل توقف
                </a>
            </div>
            <div class="col-md-3">
                <a href="#" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-graph-up"></i>إضافة مقياس
                </a>
            </div>
            <div class="col-md-3">
                <a href="#" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-file-earmark-text"></i>تقرير مفصل
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'manufacturing:equipment_list' %}" class="btn-action w-100 justify-content-center">
                    <i class="bi bi-gear-wide-connected"></i>إدارة المعدات
                </a>
            </div>
        </div>
    </div>

    <!-- أزرار العودة -->
    <div class="text-center mt-4">
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                <i class="bi bi-house me-2"></i>لوحة التحكم الرئيسية
            </a>
            <a href="{% url 'manufacturing:maintenance_dashboard' %}" class="btn btn-outline-primary">
                <i class="bi bi-tools me-2"></i>إدارة الصيانة
            </a>
        </div>
    </div>
</div>
{% endblock %}
