{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .delete-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 3rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        max-width: 600px;
        margin: 0 auto;
        text-align: center;
    }

    .warning-icon {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        margin: 0 auto 2rem;
        box-shadow: 0 15px 35px rgba(231, 76, 60, 0.3);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .delete-title {
        font-size: 2rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .delete-message {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .item-info {
        background: rgba(231, 76, 60, 0.1);
        border-radius: 15px;
        padding: 2rem;
        margin: 2rem 0;
        border-left: 5px solid #e74c3c;
    }

    .item-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .item-code {
        color: #6c757d;
        font-size: 1rem;
        font-weight: 600;
        background: rgba(231, 76, 60, 0.1);
        padding: 0.3rem 0.8rem;
        border-radius: 10px;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .item-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        text-align: left;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .detail-label {
        color: #6c757d;
        font-weight: 600;
    }

    .detail-value {
        color: #2c3e50;
        font-weight: 700;
    }

    .warning-list {
        background: rgba(243, 156, 18, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0;
        border-left: 5px solid #f39c12;
        text-align: left;
    }

    .warning-list h4 {
        color: #e67e22;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .warning-list ul {
        margin: 0;
        padding-left: 1.5rem;
    }

    .warning-list li {
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .btn-group-custom {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        margin-top: 3rem;
    }

    .btn-custom {
        padding: 1rem 2.5rem;
        border-radius: 15px;
        font-weight: 700;
        font-size: 1.1rem;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        min-width: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-danger-custom {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    }

    .btn-danger-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(231, 76, 60, 0.4);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border: 2px solid #6c757d;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    @media (max-width: 768px) {
        .delete-card {
            padding: 2rem 1.5rem;
            margin: 0 1rem;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .btn-group-custom {
            flex-direction: column;
        }
        
        .btn-custom {
            width: 100%;
        }
        
        .item-details {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-exclamation-triangle me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'production:production_line_detail' line.pk %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- بطاقة التأكيد -->
    <div class="delete-card">
        <div class="warning-icon">
            <i class="bi bi-exclamation-triangle"></i>
        </div>
        
        <h2 class="delete-title">تأكيد حذف خط الإنتاج</h2>
        
        <p class="delete-message">
            هل أنت متأكد من أنك تريد حذف خط الإنتاج التالي؟ 
            <br>
            <strong>هذا الإجراء لا يمكن التراجع عنه!</strong>
        </p>

        <!-- معلومات خط الإنتاج -->
        <div class="item-info">
            <div class="item-name">{{ line.name }}</div>
            <div class="item-code">{{ line.code }}</div>
            
            <div class="item-details">
                <div class="detail-item">
                    <span class="detail-label">المخزن:</span>
                    <span class="detail-value">{{ line.warehouse.name }}</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">الطاقة الإنتاجية:</span>
                    <span class="detail-value">{{ line.capacity_per_hour }} وحدة/ساعة</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">معدل الكفاءة:</span>
                    <span class="detail-value">{{ line.efficiency_rate }}%</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">الحالة:</span>
                    <span class="detail-value">{{ line.get_status_display }}</span>
                </div>
                
                {% if line.supervisor %}
                <div class="detail-item">
                    <span class="detail-label">المشرف:</span>
                    <span class="detail-value">{{ line.supervisor.get_full_name|default:line.supervisor.username }}</span>
                </div>
                {% endif %}
                
                {% if line.installation_date %}
                <div class="detail-item">
                    <span class="detail-label">تاريخ التركيب:</span>
                    <span class="detail-value">{{ line.installation_date }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- تحذيرات -->
        <div class="warning-list">
            <h4>
                <i class="bi bi-exclamation-circle"></i>
                تحذير: سيؤثر الحذف على:
            </h4>
            <ul>
                <li>جميع مراكز العمل المرتبطة بهذا الخط</li>
                <li>أوامر الإنتاج النشطة والمخططة</li>
                <li>البيانات التاريخية للإنتاج</li>
                <li>التقارير والإحصائيات المرتبطة</li>
            </ul>
        </div>

        <!-- أزرار الإجراءات -->
        <form method="post" style="display: inline;">
            {% csrf_token %}
            <div class="btn-group-custom">
                <button type="submit" class="btn-custom btn-danger-custom">
                    <i class="bi bi-trash"></i>
                    نعم، احذف خط الإنتاج
                </button>
                <a href="{% url 'production:production_line_detail' line.pk %}" class="btn-custom btn-secondary-custom">
                    <i class="bi bi-x-circle"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// تأكيد إضافي قبل الحذف
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد تماماً من حذف خط الإنتاج "{{ line.name }}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
