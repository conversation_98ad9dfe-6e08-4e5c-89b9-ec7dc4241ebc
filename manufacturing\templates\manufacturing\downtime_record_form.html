{% extends 'base.html' %}
{% load static %}
{% load manufacturing_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin: 2rem 0;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: rgba(231, 76, 60, 0.05);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(231, 76, 60, 0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: #e74c3c;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid rgba(231, 76, 60, 0.2);
        border-radius: 12px;
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #e74c3c;
        box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
        background: white;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
        color: white;
    }

    .btn-secondary-custom {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary-custom:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    .alert-custom {
        border: none;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-warning-custom {
        background: rgba(243, 156, 18, 0.1);
        color: #f39c12;
        border-left: 4px solid #f39c12;
    }

    .form-text-custom {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .severity-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .severity-low { background-color: #27ae60; }
    .severity-medium { background-color: #f39c12; }
    .severity-high { background-color: #e67e22; }
    .severity-critical { background-color: #e74c3c; }
    .severity-emergency { background-color: #8e44ad; }

    @media (max-width: 768px) {
        .form-container {
            padding: 2rem 1.5rem;
        }
        
        .page-title {
            font-size: 2rem;
        }
        
        .btn-group-custom {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-exclamation-triangle me-3"></i>
                    تسجيل توقف جديد
                </h1>
                <p class="text-white-50 mb-0 mt-2">تسجيل حادثة توقف في خط الإنتاج أو المعدات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house me-2"></i>الرئيسية
                    </a>
                    <a href="{% url 'manufacturing:downtime_record_list' %}" class="btn btn-light">
                        <i class="bi bi-exclamation-triangle me-2"></i>سجلات التوقف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- النموذج -->
    <div class="form-container">
        <div class="alert alert-warning-custom">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> يرجى تسجيل جميع تفاصيل التوقف بدقة لضمان التحليل الصحيح وتحسين الأداء.
        </div>

        <form method="post" id="downtimeForm">
            {% csrf_token %}
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i>
                    معلومات التوقف الأساسية
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">عنوان التوقف</label>
                            {{ form.title }}
                            <div class="form-text-custom">وصف مختصر لحادثة التوقف</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">نوع التوقف</label>
                            {{ form.downtime_type }}
                            <div class="form-text-custom">تصنيف نوع التوقف</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label required-field">خط الإنتاج</label>
                            {{ form.production_line }}
                            <div class="form-text-custom">خط الإنتاج المتأثر بالتوقف</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المعدة المتأثرة</label>
                            {{ form.equipment }}
                            <div class="form-text-custom">المعدة المحددة المتأثرة (اختياري)</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label required-field">وصف التوقف</label>
                            {{ form.description }}
                            <div class="form-text-custom">وصف تفصيلي لما حدث</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التوقيت والخطورة -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-clock"></i>
                    التوقيت والخطورة
                </h3>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label required-field">وقت بدء التوقف</label>
                            {{ form.start_time }}
                            <div class="form-text-custom">التاريخ والوقت الدقيق لبدء التوقف</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">وقت انتهاء التوقف</label>
                            {{ form.end_time }}
                            <div class="form-text-custom">اتركه فارغاً إذا كان التوقف مستمراً</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label required-field">مستوى الخطورة</label>
                            {{ form.severity }}
                            <div class="form-text-custom">
                                <span class="severity-indicator severity-low"></span>منخفضة
                                <span class="severity-indicator severity-medium"></span>متوسطة
                                <span class="severity-indicator severity-high"></span>عالية
                                <span class="severity-indicator severity-critical"></span>حرجة
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التحليل والأسباب -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-search"></i>
                    التحليل والأسباب
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">السبب الجذري</label>
                            {{ form.root_cause }}
                            <div class="form-text-custom">السبب الأساسي للتوقف (إذا كان معروفاً)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الإجراء التصحيحي</label>
                            {{ form.corrective_action }}
                            <div class="form-text-custom">الإجراءات المتخذة لحل المشكلة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">الإجراء الوقائي</label>
                            {{ form.preventive_action }}
                            <div class="form-text-custom">الإجراءات المقترحة لمنع تكرار المشكلة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التأثير والموظفون -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-people"></i>
                    التأثير والموظفون
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">أبلغ بواسطة</label>
                            {{ form.reported_by }}
                            <div class="form-text-custom">الموظف الذي أبلغ عن التوقف</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">حل بواسطة</label>
                            {{ form.resolved_by }}
                            <div class="form-text-custom">الموظف الذي حل المشكلة</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">خسارة الإنتاج</label>
                            {{ form.production_loss }}
                            <div class="form-text-custom">كمية الإنتاج المفقودة بسبب التوقف</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">التأثير على التكلفة</label>
                            {{ form.cost_impact }}
                            <div class="form-text-custom">التكلفة المالية المقدرة للتوقف</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-3 justify-content-between align-items-center">
                <!-- أزرار العودة -->
                <div class="d-flex gap-2">
                    <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-2"></i>لوحة التحكم
                    </a>
                    <a href="{% url 'manufacturing:downtime_record_list' %}" class="btn btn-outline-primary">
                        <i class="bi bi-exclamation-triangle me-2"></i>سجلات التوقف
                    </a>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="d-flex gap-3">
                    <a href="{% url 'manufacturing:downtime_record_list' %}" class="btn-secondary-custom">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                    <button type="submit" class="btn-primary-custom">
                        <i class="bi bi-check-circle"></i>
                        تسجيل التوقف
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.getElementById('downtimeForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });

    // تعيين الوقت الحالي كوقت بدء افتراضي
    const startTimeInput = form.querySelector('input[name="start_time"]');
    if (startTimeInput && !startTimeInput.value) {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000);
        startTimeInput.value = localDateTime.toISOString().slice(0, 16);
    }

    // تعيين المستخدم الحالي كمبلغ افتراضي
    const reportedBySelect = form.querySelector('select[name="reported_by"]');
    if (reportedBySelect && !reportedBySelect.value) {
        // يمكن تحسين هذا لتعيين المستخدم الحالي
    }

    // تحديث لون الخطورة عند التغيير
    const severitySelect = form.querySelector('select[name="severity"]');
    if (severitySelect) {
        severitySelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const severity = this.value;
            
            // تغيير لون الحدود حسب الخطورة
            if (severity === 'critical' || severity === 'emergency') {
                this.style.borderColor = '#e74c3c';
            } else if (severity === 'high') {
                this.style.borderColor = '#e67e22';
            } else if (severity === 'medium') {
                this.style.borderColor = '#f39c12';
            } else {
                this.style.borderColor = '#27ae60';
            }
        });
    }

    // تحديث المعدات بناءً على خط الإنتاج المختار
    const productionLineSelect = form.querySelector('select[name="production_line"]');
    const equipmentSelect = form.querySelector('select[name="equipment"]');
    
    if (productionLineSelect && equipmentSelect) {
        productionLineSelect.addEventListener('change', function() {
            const lineId = this.value;
            if (lineId) {
                // يمكن إضافة AJAX لتحديث قائمة المعدات
                // بناءً على خط الإنتاج المختار
            }
        });
    }
});
</script>
{% endblock %}
